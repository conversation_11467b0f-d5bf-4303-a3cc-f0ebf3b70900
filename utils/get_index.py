import requests

def get_taiex(date_str: str) -> float:
    """
    根據輸入的日期 (格式: YYYY-MM-DD)，查詢該日的台股加權指數，
    並回傳該日最後一筆 5 分鐘資料的收盤指數。
    
    :param date_str: 例如 '2025-05-01'
    :return: 收盤指數 (float)
    """
    # 驗證格式 & 移除破折號
    if not isinstance(date_str, str) or len(date_str) != 10 or date_str[4] != '-' or date_str[7] != '-':
        raise ValueError("輸入格式錯誤，請使用 'YYYY-MM-DD' 格式，例如 '2025-05-01'")

    query_date = date_str.replace("-", "")  # '20250501'

    url = "https://www.twse.com.tw/rwd/zh/TAIEX/MI_5MINS_HIST"
    params = {
        "date": query_date,
        "response": "json",
    }

    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-TW,zh;q=0.9",
        "x-requested-with": "XMLHttpRequest",
        "user-agent": "Mozilla/5.0",
        "referer": "https://www.twse.com.tw/zh/indices/taiex/mi-5min-hist.html"
    }

    res = requests.get(url, headers=headers, params=params)
    res.raise_for_status()
    data = res.json()

    if not data:
        raise ValueError(f"找不到 {date_str} 的資料")

    return str(data)

import requests

def get_otc(date_str: str):
    """
    查詢指定西元日期（YYYY-MM-DD）的櫃買總指數（每日收盤）

    :param date_str: 西元日期格式 'YYYY-MM-DD'
    :return: (指數名稱, 收盤指數, 漲跌點數, 漲跌百分比)
    """
    # 將 YYYY-MM-DD ➜ YYYY/MM/DD
    if not isinstance(date_str, str) or len(date_str) != 10 or date_str[4] != '-' or date_str[7] != '-':
        raise ValueError("請使用 'YYYY-MM-DD' 格式，例如 '2025-05-16'")

    query_date = date_str.replace("-", "/")

    url = "https://www.tpex.org.tw/www/zh-tw/indexInfo/sectinx"
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "x-requested-with": "XMLHttpRequest",
        "user-agent": "Mozilla/5.0"
    }
    payload = f"date={query_date}&id=&response=json"

    response = requests.post(url, headers=headers, data=payload)
    response.raise_for_status()
    data = response.json()

    if not data:
        raise ValueError(f"找不到 {date_str} 的資料")

    return str(data)
