<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>連結重新載入功能測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        #linkArea {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .refresh-btn {
            padding: 5px 10px;
            font-size: 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .refresh-btn:hover {
            background: #218838;
        }
        .refresh-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>連結重新載入功能測試</h1>
    
    <div class="test-section">
        <h2>功能說明</h2>
        <p>這個測試頁面模擬了主系統中的連結重新載入功能：</p>
        <ul>
            <li>✅ 自動重新載入：辨識完成後自動載入連結</li>
            <li>✅ 手動重新載入：用戶可以點擊按鈕手動更新</li>
            <li>✅ 載入狀態顯示：按鈕會顯示載入動畫</li>
            <li>✅ 錯誤處理：載入失敗時會顯示錯誤訊息</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>模擬連結區域</h2>
        <div id="linkArea">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h4 style="margin: 0; color: #007bff;">相關連結</h4>
                <button onclick="refreshLinks()" class="refresh-btn">
                    <i class="fas fa-sync-alt"></i> 重新載入
                </button>
            </div>
            <div id="linkContent">
                <p>點擊"重新載入"按鈕來測試功能</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>測試按鈕</h2>
        <button onclick="simulateRecognition()">模擬辨識完成</button>
        <button onclick="testAutoRefresh()">測試自動重新載入</button>
        <button onclick="clearMessages()">清除訊息</button>
    </div>

    <div class="test-section">
        <h2>訊息區域</h2>
        <div id="messages"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script>
        // 模擬連結資料
        let linkData = {
            taiex: [
                { title: "台積電技術論壇", url: "https://example.com/tsmc", news_url: "https://news.example.com/1" },
                { title: "鴻海AI布局", url: "https://example.com/foxconn", news_url: "https://news.example.com/2" }
            ],
            uss: [
                { title: "蘋果新品發表", url: "https://example.com/apple", news_url: "https://news.example.com/3" },
                { title: "微軟雲端服務", url: "https://example.com/microsoft", news_url: "https://news.example.com/4" }
            ]
        };

        let currentMode = 'taiex';

        // 顯示訊息函數
        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = type;
            messageElement.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 模擬載入連結資料
        async function loadLinkData() {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    // 模擬隨機成功/失敗
                    if (Math.random() > 0.2) {
                        showMessage('連結資料載入成功', 'success');
                        displayCurrentLinks();
                        resolve();
                    } else {
                        reject(new Error('模擬載入失敗'));
                    }
                }, 1500); // 模擬1.5秒載入時間
            });
        }

        // 顯示當前連結
        function displayCurrentLinks() {
            const linkContent = document.getElementById('linkContent');
            const currentLinks = linkData[currentMode];
            
            linkContent.innerHTML = '';
            currentLinks.forEach((link, index) => {
                const linkElement = document.createElement('div');
                linkElement.innerHTML = `
                    <p><strong>${link.title}</strong></p>
                    <p>新聞連結: <a href="${link.news_url}" target="_blank">${link.news_url}</a></p>
                    <p>投顧連結: <a href="${link.url}" target="_blank">${link.url}</a></p>
                    <hr>
                `;
                linkContent.appendChild(linkElement);
            });
        }

        // 手動重新載入連結資料
        async function refreshLinks() {
            const refreshBtn = document.querySelector('.refresh-btn');
            const originalHTML = refreshBtn.innerHTML;
            
            try {
                // 顯示載入狀態
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 載入中...';
                refreshBtn.disabled = true;
                showMessage('正在重新載入連結資料...', 'info');
                
                await loadLinkData();
                showMessage('連結資料已更新', 'success');
            } catch (error) {
                console.error('重新載入連結失敗:', error);
                showMessage('重新載入連結失敗: ' + error.message, 'error');
            } finally {
                // 恢復按鈕狀態
                refreshBtn.innerHTML = originalHTML;
                refreshBtn.disabled = false;
            }
        }

        // 模擬辨識完成
        function simulateRecognition() {
            showMessage('開始模擬辨識流程...', 'info');
            
            setTimeout(() => {
                showMessage('辨識完成，應用結果...', 'success');
                
                // 模擬 applyRecognitionResult 中的自動重新載入
                setTimeout(async () => {
                    try {
                        await loadLinkData();
                        showMessage('自動重新載入連結完成', 'success');
                    } catch (error) {
                        showMessage('自動重新載入失敗: ' + error.message, 'error');
                    }
                }, 1000);
                
                // 模擬額外的重新載入
                setTimeout(async () => {
                    try {
                        await loadLinkData();
                        showMessage('連結資料已更新', 'info');
                    } catch (error) {
                        showMessage('連結更新失敗: ' + error.message, 'error');
                    }
                }, 2000);
                
            }, 1000);
        }

        // 測試自動重新載入
        async function testAutoRefresh() {
            showMessage('測試自動重新載入功能...', 'info');
            
            setTimeout(async () => {
                try {
                    await loadLinkData();
                    showMessage('自動重新載入測試成功', 'success');
                } catch (error) {
                    showMessage('自動重新載入測試失敗: ' + error.message, 'error');
                }
            }, 1000);
        }

        // 清除訊息
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 初始化
        window.onload = function() {
            showMessage('測試頁面載入完成', 'success');
            displayCurrentLinks();
        };
    </script>
</body>
</html>
