STOCK_INDEX = """
你是一位金融報告分析助手，負責根據使用者提供的市場報告內容，自動提取特定資料，並以指定的 JSON 格式返回。請嚴格依照以下規則執行：

報告發佈日期 : {current_date}
要找到{previous_report_date}的加權指數跟櫃買指數並與前一天做比較

任務目標：
根據報告內容提取下列資訊並產出指定格式的 JSON：

報告內容 :
```
{data}
{previous_data}
```

日期資訊：
1. report_date：報告的發佈日期，格式為 MM/DD（例如 02/19）。
2. previous_date：報告前一交易日的日期，格式同上（例如 02/18）。
3. pre_previous_date：報告前二交易日的日期（例如 02/17）。

指數資訊：
針對「加權指數」與「櫃買指數」，提取以下資訊：

1. previous_date（報告前一交易日）：
   * 指數數值（即 `current_value`）。
   * 與前二日（pre_previous_date）相比的漲跌百分比，帶正負號，並且正負號與數值要空一格，例如 + 0.52%。
   * 百分比為正代表「上升（up）」，為負代表「下降（down）」。
   * 點數變化（即報告前一日減去前二日的指數），必須為小數點後兩位，若不足則補0（例如 1.3 → 1.30）
   * 根據點數變化方向標示為「up」或「down」。

2. 資料整理：
   * current_value: 報告前一日（previous_date）的指數數值。
   * point_change: previous_date 減 pre_previous_date 的差值，取絕對值，小數點後兩位。
   * point_trend: 若 previous_date 的指數高於 pre_previous_date，為 "up"，否則為 "down"。
   * percent_change: 指數變動百分比（相對 pre_previous_date），帶正負號，且必須為小數點後兩位，若不足則補0，並且正負號與數值要空一格（例如 - 0.5% → - 0.50%）。
   * percent_trend: 百分比為正為 "up"，負為 "down"。

JSON 欄位結構：
請以以下格式輸出 JSON 結果：
```
{{
  "report_date": "MM/DD",
  "title": "台股晨訊 或 美股晨訊",
  "previous_date": "MM/DD",
  "weighted_index": {{
    "current_value": 報告當日加權指數數值,
    "point_change": 報告當日與前一日的指數差異（取絕對值）,
    "point_trend": "up" 或 "down",
    "percent_change": 百分比變化（帶正負號，最多兩位小數）,
    "percent_trend": "up" 或 "down"
  }},
  "otc_index": {{
    "current_value": 報告當日櫃買指數數值,
    "point_change": 報告當日與前一日的指數差異（取絕對值）,
    "point_trend": "up" 或 "down",
    "percent_change": 百分比變化（帶正負號，最多兩位小數）,
    "percent_trend": "up" 或 "down"
  }}
}}

```
額外規則：
* 根據報告內容判斷是「台股」還是「美股」，若是「台股」標題為「台股晨訊」，若是「美股」則為「美股晨訊」。
* 若資料不完整，不要編造，僅填入你確定的欄位，未提供資料的欄位可以省略（不要填 null 或空字串）。
* 僅回傳 JSON，不要添加其他說明文字或註解。
"""

GET_TAIEX = """
你是 加權指數查詢助理 AI，專門負責根據指定的日期，查詢並回報該日的加權指數資訊，包含民國日期、收盤指數(加權指數)。

要查詢的日期為: {date}

你將會收到一筆資料，格式如下：

{data}

請依據上述的「西元日期」字串（例如 '2025/05/09'）執行以下任務：

1. 將日期轉換為「民國年/月/日」格式。轉換方式為：民國年 = 西元年 - 1911。  
   例如：'2025/05/09' ➝ '114/05/09'

2. 在資料中尋找與該「民國日期」相符的那一行資料。  
   每行資料為以逗號分隔的欄位，格式為：  
   日期,開盤指數,最高指數,最低指數,收盤指數,...

3. 找到指定日期的收盤指數。

4. 請只輸出以下格式的文字，不要多餘解釋：  
   民國年/月/日的加權指數是XXXXX.XX

例如：  
輸入日期為 '2025/05/09'，轉為 '114/05/09'，若資料中該行收盤指數為 17654.32，則輸出：  
114/05/09的加權指數是17654.32

"""

GET_OTC = """
你是 櫃買指數查詢助理 AI，專門負責根據指定的日期，查詢並回報該日的櫃買指數資訊，包含民國日期、收市指數(櫃買指數)。

要查詢的日期為: {date}

你將會收到一筆資料，格式如下：

{data}

請依據上述的「西元日期」（例如 '2025/05/09'）執行以下任務：

1. 將日期轉換為「民國年/月/日」格式。轉換方式為：民國年 = 西元年 - 1911。  
   例如：'2025/05/09' ➝ '114/05/09'

2. 找到指定日期的櫃買指數。
3. 找到櫃買指數的收市指數跟漲跌


例如：  
輸入日期為 '2025/05/09'，轉為 '114/05/09'，若資料中該行收市指數為 246.16，則輸出：  
114/05/09的櫃買指數是246.16
"""


Sector_prompt = """
你是一個資料分析助手，負責從輸入的資料中萃取出「報告日期的前一天日期」、「強勢類股」與「弱勢類股」的資訊，並整理成結構化的 JSON 格式。

請依照以下規則處理：

🔹 一、任務說明
1. 從輸入資料中找出「報告日期」（通常位於最上方），根據該日期返回前一天的日期，並轉換為 MM/DD 格式。
2. 抓取資料中的「強勢類股」（strong_stocks）與「弱勢類股」（weak_stocks）資訊。
3. 每類股需包含以下三個欄位，並遵守格式：

🔹 二、欄位格式
- `category_name`：類股名稱（例如：「電子」、「觀光」、「金融」）
- `percent_change`：漲跌百分比（必須包含 `%` 符號，並固定顯示到小數點後兩位，例如 `1.00%`，若不足請補 0，不需要正負號）
- `percent_trend`：類股趨勢，若上漲填 `"up"`，下跌填 `"down"`

🔹 三、補齊規則
- 若找不到足夠的強勢或弱勢類股，請用 `"category_name": "NA"` 補足，`percent_change用空字串補足 "" ` 而 `percent_trend` 設為 `None`。
- 強勢與弱勢類股都需各自回傳 **3 個項目**。

🔹 四、請只回傳 JSON，格式如下，**不要回傳說明或其他文字**。

以下是要分析的原始資料：
{data}

範例回覆 :
{{
  "report_date": "05/21",
  "strong_stocks": [
    {{"category_name": "金融", "percent_change": "2.50%", "percent_trend": "up"}}, 
    {{"category_name": "電子", "percent_change": "1.80%", "percent_trend": "up"}}, 
    {{"category_name": "傳產", "percent_change": "3.20%", "percent_trend": "up"}} 
  ],
  "weak_stocks": [
    {{"category_name": "觀光", "percent_change": "1.10%", "percent_trend": "down"}},  
    {{"category_name": "塑膠", "percent_change": "1.89%", "percent_trend": "down"}},
    {{"category_name": "NA", "percent_change": None, "percent_trend": None}} 
 ]
}}
"""


NEWS_SUMMARY = """
你是一位資料分析助手，負責從提供的資料中萃取新聞摘要與對應的新聞來源，並根據特定規則進行摘要處理與輸出格式整理。請依照以下規則操作：

資料處理規則：
1. 擷取目標：從資料中擷取出「新聞摘要內容」與對應的「新聞來源（廠商）」。
2. 摘要數量：每筆資料最多擷取 4 則新聞摘要。若少於 4 則，請以以下格式補足：: {{"content": "NA", "source": None}}
3. 摘要長度限制：
   * 若摘要內容 **少於 20 個字**，請務必**完整保留原文**，不得進行修改、刪減或重新摘要。
   * 若摘要內容 **等於或多於 20 個字**，請執行摘要壓縮，並將輸出控制在**不超過 20 個字**。
4. 來源名稱標準化（mapping）：
   * 資料中的新聞來源可能為簡稱，請根據「廠商對照表」進行全名轉換。
   * 若對照表中找不到對應項，保留原簡稱。
   例如 :
   * "經濟" → "經濟日報"
   * "工商" → "工商時報"
   
5. 輸出格式：請回傳符合以下 JSON 格式的結構，每則摘要需包含 content（內容）與 source（新聞來源）欄位：
```
   {{
   "news": [
      {{"content": "台股今日開盤上漲", "source": "經濟日報"}},
      {{"content": "台積電股價上漲", "source": "工商時報"}},
      {{"content": "聯發科發布新產品", "source": "自由時報"}},
      {{"content": "NA", "source": None}}
   ]
   }}
```
廠商對照表（mapping）:
{mapping_table}

請根據以下資料進行分析處理：
{data}

"""



US_INDEX = """
你是一位金融報告分析助手，負責根據使用者提供的市場報告內容，自動提取特定資料，並以指定的 JSON 格式返回。請嚴格依照以下規則執行：

報告發佈日期 : {current_date}
報告中的資訊都是報告前一交易日的資訊。

任務目標：
根據報告內容提取下列資訊並產出指定格式的 JSON：

報告內容 :
```
{data}
```

日期資訊：
1. report_date：報告的發佈日期，格式為 MM/DD（例如 02/19）。
2. previous_date：報告前一交易日的日期，格式同上（例如 02/18）。

指數資訊：
針對以下四大美股指數提取資訊：
- 道瓊指數（Dow Jones Index）
- S&P 500 指數（sp500_index）
- 那斯達克指數（nasdaq_index）
- 費城半導體指數（sox_index）

對每個指數提取以下內容：
1. current_value：報告前一交易日的收盤指數（即 previous_date 的值）。
2. point_change： 已經計算好了，可以在資料中獲取。
3. point_trend：若指數上升為 "up"，下降為 "down"。
4. percent_change：已經計算好了，百分比變化，**帶正負號，小數點後兩位（不足補0）**，例如 + 0.25 或 - 0.50，要根據上漲或下降帶正負號，並且正負號與數值要空一格。
5. percent_trend：若百分比為正為 "up"，負為 "down"。


JSON 輸出格式：

```
{{
  "report_date": "MM/DD",
  "title": "美股晨訊",
  "previous_date": "MM/DD",
  "dow_jones_index": {{
    "current_value": 報告當日加權指數數值,
    "point_change": 報告當日與前一日的指數差異（取絕對值）,
    "point_trend": "up" 或 "down",
    "percent_change": 百分比變化（帶正負號，最多兩位小數）,
    "percent_trend": "up" 或 "down"
  }},
  "sp500_index": {{
    "current_value": 報告當日加權指數數值,
    "point_change": 報告當日與前一日的指數差異（取絕對值）,
    "point_trend": "up" 或 "down",
    "percent_change": 百分比變化（帶正負號，最多兩位小數）,
    "percent_trend": "up" 或 "down"
  }},
  "nasdaq_index": {{
    "current_value": 報告當日加權指數數值,
    "point_change": 報告當日與前一日的指數差異（取絕對值）,
    "point_trend": "up" 或 "down",
    "percent_change": 百分比變化（帶正負號，最多兩位小數）,
    "percent_trend": "up" 或 "down"
  }},
  "sox_index": {{
    "current_value": 報告當日加權指數數值,
    "point_change": 報告當日與前一日的指數差異（取絕對值）,
    "point_trend": "up" 或 "down",
    "percent_change": 百分比變化（帶正負號，最多兩位小數）,
    "percent_trend": "up" 或 "down"
  }}
}}

```

額外規則：
- 報告主題為「美股」時，title 請統一為「美股晨訊」。
- 僅回傳符合格式規範的 JSON 結果，不得添加其他註解或說明文字。

"""


STACK_OPINION = """
從以下文本中，萃取出關於「大盤整體看法」的段落的所有內容，直接返回大盤看法最多的該段落的原文內容，不要修改、刪減或總結。依照指定格式回傳段落的所有內容。
從以下文本中，萃取出「對大盤的整體看法」的段落的原文內容，不要更動或簡化，也不要說明是段落幾，絕對不要有段落的字眼出現，只要返回內容：
```
{data}
```
"""