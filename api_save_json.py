from fastapi import FastAPI, File, UploadFile, Request, Response, Query
from fastapi.responses import JSONResponse
from key import OPENAI_API_KEY
from utils.taiex import taiex1, taiex2, taiex3, taiex4, taiex5
from docx import Document
from io import BytesIO
from utils.us import us1, us2, us3, extract_and_clean_focus_stocks, us4_5
import os
import json
from datetime import datetime
from fastapi.staticfiles import StaticFiles
from pathlib import Path
from typing import List, Dict, Any, Optional

app = FastAPI()

# 確保日誌目錄存在
log_dir = Path("/home/<USER>/ai_env/sam/project/stock_img/log")
log_dir.mkdir(exist_ok=True)

# 添加 OPTIONS 請求處理
@app.options("/{path:path}")
async def options_handler(request: Request, path: str):
    return Response(status_code=200)

# 掛載 info 目錄為靜態文件
app.mount("/info", StaticFiles(directory="/home/<USER>/ai_env/sam/project/stock_img/info"), name="info")

# 檢查當天的 JSON 文件
@app.get("/check_today_json")
async def check_today_json():
    try:
        # 獲取當天日期 (YYYYMMDD 格式)
        today = datetime.now().strftime("%Y%m%d")
        info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
        today_dir = os.path.join(info_dir, today)
        
        result = {
            "date": today,
            "taiwan_exists": False,
            "us_exists": False,
            "taiwan_data": None,
            "us_data": None
        }
        
        # 檢查目錄是否存在
        if not os.path.exists(today_dir):
            return result
        
        # 檢查台灣股市數據
        taiwan_path = os.path.join(today_dir, "taiwan.json")
        if os.path.exists(taiwan_path):
            result["taiwan_exists"] = True
            with open(taiwan_path, "r", encoding="utf-8") as f:
                result["taiwan_data"] = json.load(f)
        
        # 檢查美國股市數據
        us_path = os.path.join(today_dir, "us.json")
        if os.path.exists(us_path):
            result["us_exists"] = True
            with open(us_path, "r", encoding="utf-8") as f:
                result["us_data"] = json.load(f)
        
        return result
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": f"檢查 JSON 文件失敗: {str(e)}"})

@app.post("/stock_index")
async def upload_files(files: list[UploadFile] = File(...)):
    if len(files) != 2:
        return JSONResponse(status_code=400, content={"error": "請同時上傳一個 .txt 和一個 .docx 檔案"})

    txt_lines = None
    docx_text = None

    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})

    # 檢查是否成功解析兩個格式
    if txt_lines is None or docx_text is None:
        return JSONResponse(status_code=400, content={"error": "需同時上傳一個 .txt 檔與一個 .docx 檔"})

    # 執行分析
    taiex1_result = taiex1(txt_lines, OPENAI_API_KEY)
    taiex2_result = taiex2(txt_lines, OPENAI_API_KEY)
    taiex3_result = taiex3(docx_text)
    to_crawl_taiex5, taiex4_result = taiex4(txt_lines, OPENAI_API_KEY)
    taiex5_result, result_tw_urls = await taiex5(to_crawl_taiex5, OPENAI_API_KEY)
    print(f"result_tw_urls: {result_tw_urls}")

    final_result = taiex1_result + taiex2_result + taiex3_result + taiex4_result + taiex5_result
    
    # 保存結果到 info 目錄
    try:
        # 創建當天的目錄 (YYYYMMDD 格式)
        today = datetime.now().strftime("%Y%m%d")
        info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
        today_dir = os.path.join(info_dir, today)
        os.makedirs(today_dir, exist_ok=True)
        
        # 保存結果到 taiwan.json
        json_path = os.path.join(today_dir, "taiwan.json")
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存 taiwan.json 失敗: {str(e)}")
        # 如果保存失敗，檢查是否有原來的文件可以使用
        # 但不影響 API 返回結果
    
    return final_result


@app.post("/uss_index")
async def upload_uss_files(files: list[UploadFile] = File(...)):
    if len(files) != 2:
        return JSONResponse(status_code=400, content={"error": "請同時上傳一個 .txt 和一個 .docx 檔案"})

    txt_lines = None
    docx_text = None

    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
                # docx_text = '\n'.join([f"段落{idx + 1} : {p.text.strip()}" for idx, p in enumerate(doc.paragraphs) if p.text.strip()])

            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})

    if txt_lines is None or docx_text is None:
        return JSONResponse(status_code=400, content={"error": "需同時上傳一個 .txt 檔與一個 .docx 檔"})

    us4_data = extract_and_clean_focus_stocks(txt_lines)
    us1_result = us1(txt_lines, OPENAI_API_KEY)
    us2_result = us2(txt_lines, OPENAI_API_KEY)
    us3_result = us3(docx_text)
    us4_5_result = await us4_5(us4_data, OPENAI_API_KEY)
    us4_result = us4_5_result[0]
    us5_result = us4_5_result[1]
    us5_urls = us4_5_result[2]
    
    final_result = us1_result + us2_result + us3_result + us4_result + us5_result
    
    # 保存結果到 info 目錄
    try:
        # 創建當天的目錄 (YYYYMMDD 格式)
        today = datetime.now().strftime("%Y%m%d")
        info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
        today_dir = os.path.join(info_dir, today)
        os.makedirs(today_dir, exist_ok=True)
        
        # 保存結果到 us.json
        json_path = os.path.join(today_dir, "us.json")
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存 us.json 失敗: {str(e)}")
        # 如果保存失敗，檢查是否有原來的文件可以使用
        # 但不影響 API 返回結果
    
    return final_result

@app.post("/test")
async def upload_txt(file: UploadFile = File(...)):
    if not file.filename.endswith(".txt"):
        return JSONResponse(status_code=400, content={"error": "Only .txt files are allowed."})

    content = await file.read()
    text = content.decode("utf-8")

    lines = text.strip().splitlines()
    if not lines:
        return JSONResponse(status_code=400, content={"error": "File is empty."})
   
    result =[[{'type': 'text', 'value': '05/16', 'x': 258, 'y': 58, 'fontSize': 24, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '台股晨訊', 'x': 152, 'y': 110, 'fontSize': 57, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '05/15', 'x': 139, 'y': 165, 'fontSize': 13, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '21730.25', 'x': 153, 'y': 253, 'fontSize': 26, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '52.62', 'x': 176, 'y': 282, 'fontSize': 26, 'fill': '#00A600'}, {'type': 'image', 'value': 'down', 'x': 85, 'y': 270, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '-0.24%', 'x': 168, 'y': 313, 'fontSize': 26, 'fill': '#00A600'}, {'type': 'text', 'value': '230.13', 'x': 170, 'y': 395, 'fontSize': 26, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.34', 'x': 184, 'y': 428, 'fontSize': 26, 'fill': '#00A600'}, {'type': 'image', 'value': 'down', 'x': 85, 'y': 418, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '-0.58%', 'x': 169, 'y': 460, 'fontSize': 26, 'fill': '#00A600'}], [{'type': 'text', 'value': '05/16', 'x': 41, 'y': 55, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '航運業', 'x': 85, 'y': 168, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.06%', 'x': 93, 'y': 203, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'up', 'x': 18, 'y': 190, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '電機機械', 'x': 85, 'y': 298, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '0.82%', 'x': 93, 'y': 335, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'up', 'x': 18, 'y': 323, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '電器電纜', 'x': 85, 'y': 430, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '0.77%', 'x': 93, 'y': 463, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'up', 'x': 18, 'y': 450, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '紡織纖維', 'x': 240, 'y': 168, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.84%', 'x': 247, 'y': 201, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'down', 'x': 175, 'y': 190, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '造紙', 'x': 240, 'y': 298, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.68%', 'x': 247, 'y': 333, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'down', 'x': 175, 'y': 323, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '汽車', 'x': 240, 'y': 430, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.48%', 'x': 247, 'y': 463, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'down', 'x': 175, 'y': 450, 'fontSize': None, 'height': None}], [{'type':'text','value':'周四大盤終結連續四天收漲，顯示調節賣壓增強，不排除周五盤中仍有拉回的可能，若跌破周四低點21674點，下檔將回測周三帶量長紅低點21552點，若能守住短多仍占優勢，上檔就有望挑戰2萬2甚至半年線以及年線。周四台積電、鴻海連袂收跌拖累大盤，周五須關注台積電、鴻海是否能止穩彈升收紅K。貨櫃航運股長榮、萬海留下長上影線作收，周五走勢宜漲不宜跌。AI概念股偏重個股表現，飛機零組件族群在整理數日之後出現發動跡象，近期走勢可持續加以關注。','x':157,'y':279,'fontSize': 20, 'fill': '#FFFFFF'}], [{'type': 'text', 'value': '台積技術論壇 揭先進製程藍圖', 'x': 172, 'y': 145, 'fontSize': 23, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '工商時報', 'x': 181, 'y': 98, 'fontSize': 21, 'fill': '#FFFFFF'}, {'type': 'text', 'value': 'AI助威 思科調高年度財測', 'x': 172, 'y': 250, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '經濟日報', 'x': 181, 'y': 212, 'fontSize': 21, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '萬海美線訂單艙需求猛增五成', 'x': 172, 'y': 365, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '工商時報', 'x': 181, 'y': 329, 'fontSize': 21, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '皇普喜迎完工交屋潮', 'x': 172, 'y': 482, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '經濟日報', 'x': 181, 'y': 448, 'fontSize': 21, 'fill': '#FFFFFF'}], []]

    return result

# 獲取連結資料的 API 端點
@app.get("/get_url_json")
async def get_url_json(type: str, date: str = None):
    try:
        # 如果沒有提供日期，使用當天日期
        if not date:
            date = datetime.now().strftime("%Y%m%d")
        
        # 檢查日期格式是否正確
        if not (len(date) == 8 and date.isdigit()):
            return JSONResponse(status_code=400, content={"error": "日期格式不正確，應為 YYYYMMDD"})
        
        # 構建文件路徑
        urls_dir = "/home/<USER>/ai_env/sam/project/stock_img/urls"
        date_dir = os.path.join(urls_dir, date)
        
        # 檢查目錄是否存在
        if not os.path.exists(date_dir):
            return JSONResponse(status_code=404, content={"error": f"找不到日期 {date} 的連結資料"})
        
        # 根據類型讀取不同的文件
        if type == "taiex":
            file_path = os.path.join(date_dir, "taiex.json")
        elif type == "us":
            file_path = os.path.join(date_dir, "us.json")
        else:
            return JSONResponse(status_code=400, content={"error": "類型必須是 'taiex' 或 'us'"})
        
        # 檢查文件是否存在
        if not os.path.exists(file_path):
            return JSONResponse(status_code=404, content={"error": f"找不到 {type} 的連結資料"})
        
        # 讀取文件內容
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        return data
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": f"獲取連結資料失敗: {str(e)}"})

# 日誌記錄 API 端點
@app.post("/log")
@app.options("/log")  # 明確添加對 OPTIONS 請求的支持
async def log_event(log_data: Dict[str, Any] = None):
    # 如果是 OPTIONS 請求，data 將為 None
    if log_data is None:
        return {"success": True}
    try:
        # 獲取當前日期的日誌文件路徑
        file_path = log_dir / f"log.json"
        
        # 確保日誌目錄存在
        log_dir.mkdir(exist_ok=True)
        
        # 讀取現有日誌
        logs = []
        if file_path.exists():
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    logs = json.load(f)
            except json.JSONDecodeError:
                # 如果文件格式錯誤，使用空列表
                logs = []
        
        # 添加新日誌
        logs.append(log_data)
        
        # 寫入日誌文件
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
        
        print(f"日誌已記錄到文件: {file_path}")
        
        return {"success": True}
    except Exception as e:
        print(f"記錄日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"記錄日誌失敗: {str(e)}"})

# 批量日誌記錄 API 端點
@app.post("/log/batch")
@app.options("/log/batch")  # 明確添加對 OPTIONS 請求的支持
async def log_batch(data: Dict[str, List[Dict[str, Any]]] = None):
    # 如果是 OPTIONS 請求，data 將為 None
    if data is None:
        return {"success": True}
    try:
        logs = data.get("logs", [])
        
        if not logs:
            return {"success": True, "count": 0, "message": "沒有日誌需要記錄"}
        
        print(f"收到 {len(logs)} 條日誌進行批量記錄")
        
        # 確保日誌目錄存在
        log_dir.mkdir(exist_ok=True)
        
        # 按日期分組日誌
        logs_by_date = {}
        for log in logs:
            # 從時間戳中提取日期
            timestamp = log.get("timestamp", "")
            try:
                date = datetime.fromisoformat(timestamp.replace("Z", "+00:00")).strftime("%Y%m%d")
            except (ValueError, AttributeError):
                # 如果時間戳格式錯誤，使用當前日期
                date = datetime.now().strftime("%Y%m%d")
            
            if date not in logs_by_date:
                logs_by_date[date] = []
            
            logs_by_date[date].append(log)
        
        # 將日誌寫入相應的文件
        for date, date_logs in logs_by_date.items():
            file_path = log_dir / f"log.json"
            
            # 讀取現有日誌
            existing_logs = []
            if file_path.exists():
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        existing_logs = json.load(f)
                except json.JSONDecodeError:
                    # 如果文件格式錯誤，使用空列表
                    existing_logs = []
            
            # 添加新日誌
            existing_logs.extend(date_logs)
            
            # 寫入日誌文件
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(existing_logs, f, ensure_ascii=False, indent=2)
            
            print(f"已將 {len(date_logs)} 條日誌寫入文件: {file_path}")
        
        return {"success": True, "count": len(logs)}
    except Exception as e:
        print(f"批量記錄日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"批量記錄日誌失敗: {str(e)}"})

# 獲取日誌 API 端點
@app.get("/logs")
async def get_logs(start_date: Optional[str] = None, end_date: Optional[str] = None,
                   actions: List[str] = Query(default=[]), username: Optional[str] = None):
    try:
        # 讀取日誌文件
        file_path = log_dir / "log.json"
        if not file_path.exists():
            return {"success": True, "logs": []}
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                all_logs = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"讀取日誌文件失敗: {str(e)}")
            return {"success": False, "error": f"讀取日誌文件失敗: {str(e)}"}
        
        # 根據條件過濾日誌
        filtered_logs = all_logs
        
        # 按日期過濾
        if start_date or end_date:
            filtered_logs = []
            for log in all_logs:
                try:
                    # 將帶時區的 ISO 格式時間轉換為不帶時區的本地時間
                    timestamp = log.get("timestamp", "")
                    if timestamp:
                        # 移除 Z 並轉換為 datetime 對象
                        if timestamp.endswith("Z"):
                            timestamp = timestamp[:-1]  # 移除 Z
                        log_date = datetime.fromisoformat(timestamp)
                        
                        # 轉換為本地時間字符串 (YYYYMMDD)
                        log_date_str = log_date.strftime("%Y%m%d")
                        
                        # 比較日期字符串而不是 datetime 對象
                        if start_date and log_date_str < start_date:
                            continue
                        
                        if end_date and log_date_str > end_date:
                            continue
                    
                    filtered_logs.append(log)
                except (ValueError, TypeError) as e:
                    print(f"日期解析錯誤: {str(e)}")
                    # 如果日期解析錯誤，保留該日誌
                    if not start_date and not end_date:
                        filtered_logs.append(log)
        
        # 按事件類型過濾（支援多選）
        if actions:
            filtered_logs = [log for log in filtered_logs if log.get("action") in actions]
        
        # 按用戶名過濾
        if username:
            filtered_logs = [log for log in filtered_logs if log.get("username") and username in log.get("username")]
        
        # 按時間戳排序（最新的在前）
        filtered_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        return {"success": True, "logs": filtered_logs}
    except Exception as e:
        print(f"獲取日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"獲取日誌失敗: {str(e)}"})

# 導出日誌 API 端點
@app.get("/export_logs")
async def export_logs(start_date: Optional[str] = None, end_date: Optional[str] = None,
                      actions: List[str] = Query(default=[]), username: Optional[str] = None):
    try:
        # 讀取日誌文件
        file_path = log_dir / "log.json"
        if not file_path.exists():
            return {"success": True, "logs": []}

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                all_logs = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"讀取日誌文件失敗: {str(e)}")
            return {"success": False, "error": f"讀取日誌文件失敗: {str(e)}"}

        # 根據條件過濾日誌
        filtered_logs = all_logs

        # 按日期過濾
        if start_date or end_date:
            filtered_logs = []
            for log in all_logs:
                try:
                    # 將帶時區的 ISO 格式時間轉換為不帶時區的本地時間
                    timestamp = log.get("timestamp", "")
                    if timestamp:
                        # 移除 Z 並轉換為 datetime 對象
                        if timestamp.endswith("Z"):
                            timestamp = timestamp[:-1]  # 移除 Z
                        log_date = datetime.fromisoformat(timestamp)

                        # 轉換為本地時間字符串 (YYYYMMDD)
                        log_date_str = log_date.strftime("%Y%m%d")

                        # 比較日期字符串而不是 datetime 對象
                        if start_date and log_date_str < start_date:
                            continue

                        if end_date and log_date_str > end_date:
                            continue

                    filtered_logs.append(log)
                except (ValueError, TypeError) as e:
                    print(f"日期解析錯誤: {str(e)}")
                    # 如果日期解析錯誤，保留該日誌
                    if not start_date and not end_date:
                        filtered_logs.append(log)

        # 按事件類型過濾（支援多選）
        if actions:
            filtered_logs = [log for log in filtered_logs if log.get("action") in actions]

        # 按用戶名過濾
        if username:
            filtered_logs = [log for log in filtered_logs if log.get("username") and username in log.get("username")]

        # 按時間戳排序（最新的在前）
        filtered_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)

        return {"success": True, "logs": filtered_logs}
    except Exception as e:
        print(f"導出日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"導出日誌失敗: {str(e)}"})

if __name__ == "__main__":
    import uvicorn
    # uvicorn.run("api:app", host="0.0.0.0" , port=4512, reload=True)
    uvicorn.run(app, host="0.0.0.0" , port=4512)