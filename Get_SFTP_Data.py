import paramiko 
import os 
from datetime import datetime, timedelta 
 
# SFTP 伺服器資訊 
hostname = 'sectrans.kgi.com.tw' 
port = 22 
username = 'EMERUSER' 
password = '2F*BU8'  # 向 William 或 Sam 取得後填入這裡 
 
# 取得今日日期字串，格式為 YYYYMMDD 
today_str = datetime.now().strftime('%Y%m%d') 
# 取得前日日期字串，格式為 YYYYMMDD 
yesterday_str = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d') 
print(f"今日日期: {today_str}")
print(f"前日日期: {yesterday_str}")

# 建立資料夾 
daily_dir = os.path.join('daily_data', today_str) 
recog_dir = os.path.join('recog_data', yesterday_str) 
 
os.makedirs(daily_dir, exist_ok=True) 
os.makedirs(recog_dir, exist_ok=True) 
 
# 建立 SSH/SFTP 連線 
try: 
    transport = paramiko.Transport((hostname, port)) 
    transport.connect(username=username, password=password) 
 
    sftp = paramiko.SFTPClient.from_transport(transport) 
 
    # 列出主目錄檔案 
    files = sftp.listdir('.') 
    print("遠端主目錄檔案：") 
    print(f"尋找CSV檔案包含日期 {today_str} 和 TXT/DOCX檔案包含日期 {yesterday_str} 的檔案...") 
     
    downloaded_count = 0 
     
    for f in files: 
        print(f"檢查檔案: {f}") 
         
        # 根據檔案類型決定下載位置和日期篩選條件
        if f.endswith('.csv'): 
            # CSV檔案：只處理檔名包含今日日期的檔案
            if today_str not in f: 
                print(f"  跳過 (CSV檔案不包含今日日期 {today_str})") 
                continue 
            local_path = os.path.join(daily_dir, f) 
        elif f.endswith('.txt') or f.endswith('.docx'): 
            # TXT/DOCX檔案：只處理檔名包含前日日期的檔案
            if yesterday_str not in f: 
                print(f"  跳過 (TXT/DOCX檔案不包含前日日期 {yesterday_str})") 
                continue 
            local_path = os.path.join(recog_dir, f) 
        else: 
            print(f"  跳過 (不支援的檔案類型)") 
            continue 
 
        # 從遠端下載檔案到本機 
        try: 
            sftp.get(f, local_path) 
            print(f"  已下載: {f} → {local_path}") 
            downloaded_count += 1 
        except Exception as e: 
            print(f"  下載失敗: {f} - {e}") 
 
    print(f"\n總共下載了 {downloaded_count} 個檔案") 
 
    # 關閉連線 
    sftp.close() 
    transport.close() 
 
except Exception as e: 
    print("連線失敗：", e)