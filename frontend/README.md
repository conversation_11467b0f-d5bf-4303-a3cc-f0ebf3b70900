# 多圖辨識編輯器

這是一個支援台指和美股的多圖辨識編輯器系統，具有用戶登入和權限管理功能。

## 功能特點

- 用戶登入與權限管理（管理員/一般用戶）
- 台指和美股圖片辨識
- 圖片編輯與匯出
- 管理員可訪問設定頁面進行用戶管理

## 系統架構

- 前端：HTML, CSS, JavaScript
- 後端：Node.js, Express
- 資料庫：MongoDB

## 安裝與設定

### 前置需求

- Node.js (v14+)
- npm

### 安裝步驟

1. 安裝依賴項：

```bash
npm install
```

2. 初始化資料庫（創建預設用戶）：

```bash
node init-db.js
```

3. 啟動後端服務：

```bash
npm start
```

4. 在瀏覽器中訪問：

```
http://localhost:6535/login.html
```

## 預設用戶

系統初始化後會創建以下預設用戶：

- 管理員：admin / admin123
- 一般用戶：user1 / user123

## 文件結構

- `login.html` - 登入頁面
- `index.html` - 主頁面（多圖辨識編輯器）
- `settings.html` - 設定頁面（僅管理員可訪問）
- `auth-api.js` - 身份驗證API
- `user-api.js` - 用戶管理API
- `init-db.js` - 資料庫初始化腳本

## MongoDB 設定

系統使用以下MongoDB連接：

```
mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot
```

集合名稱：`KG_Stack`

## 使用說明

1. 訪問登入頁面，輸入用戶名和密碼
2. 登入成功後進入主頁面
3. 管理員可以點擊設定按鈕進入設定頁面
4. 在設定頁面可以管理用戶（新增/刪除）
5. 使用主頁面進行圖片辨識和編輯

## 注意事項

- 本系統未實現註冊功能，只能通過管理員在設定頁面新增用戶
- 密碼以明文形式存儲在資料庫中，僅供演示使用