{"name": "stock-img-frontend", "version": "1.0.0", "description": "多圖辨識編輯器前端", "main": "index.js", "scripts": {"start-auth": "PORT=6535 node auth-api.js", "start-user": "PORT=6536 node user-api.js", "start": "concurrently \"npm run start-auth\" \"npm run start-user\""}, "dependencies": {"body-parser": "^1.20.2", "concurrently": "^8.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.18.2", "mongodb": "^5.7.0"}}