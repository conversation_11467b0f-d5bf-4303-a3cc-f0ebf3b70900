<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KGIS AI圖文生成平台 - 登入</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+HK:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #3498db;
      --primary-dark: #2980b9;
      --secondary-color: #2ecc71;
      --secondary-dark: #27ae60;
      --danger-color: #e74c3c;
      --danger-dark: #c0392b;
      --text-color: #333;
      --light-bg: #f8f9fa;
      --border-color: #e0e0e0;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --radius: 8px;
      --transition: all 0.3s ease;
    }
    
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Noto Sans HK', sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-color);
    }
    
    .login-container {
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 400px;
      position: relative;
      overflow: hidden;
    }
    
    .login-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .login-header h1 {
      color: var(--primary-dark);
      margin: 0 0 10px 0;
      font-size: 28px;
      font-weight: 600;
    }
    
    .login-header p {
      color: #666;
      margin: 0;
      font-size: 14px;
    }
    
    .form-group {
      margin-bottom: 20px;
      position: relative;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .form-group input {
      width: 100%;
      padding: 12px 45px 12px 15px;
      border: 2px solid var(--border-color);
      border-radius: var(--radius);
      font-size: 16px;
      font-family: inherit;
      transition: var(--transition);
      background: white;
    }
    
    .form-group input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    
    .form-group .input-icon {
      position: absolute;
      right: 15px;
      top: 46px;
      color: #999;
      font-size: 18px;
      pointer-events: none;
    }
    
    .login-btn {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      border-radius: var(--radius);
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .login-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    }
    
    .login-btn:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 10px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-message {
      background: #fee;
      border: 1px solid #fcc;
      color: var(--danger-color);
      padding: 12px 15px;
      border-radius: var(--radius);
      margin-bottom: 20px;
      font-size: 14px;
      display: none;
      animation: fadeIn 0.3s ease;
    }
    
    .error-message.show {
      display: block;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .demo-accounts {
      margin-top: 30px;
      padding: 20px;
      background: var(--light-bg);
      border-radius: var(--radius);
      border: 1px solid var(--border-color);
    }
    
    .demo-accounts h4 {
      margin: 0 0 15px 0;
      color: var(--primary-dark);
      font-size: 14px;
      text-align: center;
    }
    
    .demo-account {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #ddd;
      font-size: 13px;
    }
    
    .demo-account:last-child {
      border-bottom: none;
    }
    
    .demo-account .role {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }
    
    .demo-account .role.admin {
      background: #e8f5e8;
      color: var(--secondary-dark);
    }
    
    .demo-account .role.user {
      background: #e8f4f8;
      color: var(--primary-dark);
    }
    
    @media (max-width: 480px) {
      .login-container {
        margin: 20px;
        padding: 30px 25px;
      }
      
      .login-header h1 {
        font-size: 24px;
      }
    }
  </style>
  <script src="js/logger.js"></script>
</head>
<body>
  <div class="login-container">
    <div class="login-header">
      <h1><i class="fas fa-chart-line"></i> KGIS AI圖文生成平台</h1>
      <p>請輸入您的帳號密碼登入系統</p>
    </div>
    
    <div id="errorMessage" class="error-message"></div>
    
    <form id="loginForm">
      <div class="form-group">
        <label for="username"><i class="fas fa-user"></i> 帳號</label>
        <input type="text" id="username" name="username" required autocomplete="username">
        <i class="fas fa-user input-icon"></i>
      </div>
      
      <div class="form-group">
        <label for="password"><i class="fas fa-lock"></i> 密碼</label>
        <input type="password" id="password" name="password" required autocomplete="current-password">
        <i class="fas fa-lock input-icon"></i>
      </div>
      
      <button type="submit" id="loginBtn" class="login-btn">
        <i class="fas fa-sign-in-alt"></i> 登入
      </button>
    </form>
    
  </div>

  <script>
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const errorMessage = document.getElementById('errorMessage');
    
    // 檢查是否已經登入
    const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
    if (currentUser) {
      window.location.href = 'index.html'; // 重導向到主頁面
    }
    
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value;
      
      if (!username || !password) {
        showError('請輸入帳號和密碼');
        return;
      }
      
      // 顯示載入狀態
      loginBtn.disabled = true;
      loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登入中';
      hideError();
      
      try {
        // 直接使用遠程API服務器
        const response = await fetch('https://bot.agatha-ai.com/flowise/02224165-5970-429f-a870-b835056ca4d4/auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: username,
            password: password,
            dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
            collection: "KG_Stack"
          })
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          // 檢查用戶狀態，使用更嚴格的條件，只有明確設置為true的用戶才能登入
          if (result.user.status !== true) {
            showError('此帳號已被停用，請聯繫管理員');
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登入';
            return;
          }
          
          // 登入成功，儲存用戶資訊
          const userInfo = {
            username: result.user.username,
            role: result.user.role,
            userCode: result.user.userCode || '',
            systemCode: result.user.systemCode || 'ZZ999',
            status: result.user.status !== false, // 確保狀態資訊也被保存
            loginTime: new Date().toISOString()
          };
          
          sessionStorage.setItem('loginStatus', JSON.stringify(userInfo));
          
          // 顯示成功訊息
          loginBtn.innerHTML = '<i class="fas fa-check"></i> 登入成功！';
          
          // 記錄登入事件，並在完成後再重導向
          try {
            if (window.Logger) {
              // 使用 await 等待記錄完成後再重導向
              await Logger.logLogin(username)
                .then(result => {
                  // 記錄成功後再重導向
                  setTimeout(() => {
                    window.location.href = 'index.html';
                  }, 1000);
                })
                .catch(error => {
                  //console.error(error);
                  // 即使記錄失敗也要重導向，但給更多時間嘗試本地存儲
                  setTimeout(() => {
                    window.location.href = 'index.html';
                  }, 2000);
                });
            } else {
              console.error('Logger 未定義，無法記錄登入事件');
              // Logger 未定義時直接重導向
              setTimeout(() => {
                window.location.href = 'index.html';
              }, 1500);
            }
          } catch (logError) {
            //console.error(logError);
            // 發生錯誤時也要重導向
            setTimeout(() => {
              window.location.href = 'index.html';
            }, 1500);
          }
          
        } else {
          showError(result.message || '帳號或密碼錯誤');
        }
        
      } catch (error) {
        //console.error(error);
        showError('連線失敗，請確保API服務器已啟動或檢查網絡連接');
      } finally {
        if (loginBtn.innerHTML.includes('登入中')) {
          loginBtn.disabled = false;
          loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登入';
        }
      }
    });
    
    function showError(message) {
      errorMessage.textContent = message;
      errorMessage.classList.add('show');
    }
    
    function hideError() {
      errorMessage.classList.remove('show');
    }
    
    // 按 Enter 鍵提交表單
    document.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !loginBtn.disabled) {
        loginForm.dispatchEvent(new Event('submit'));
      }
    });
  </script>
</body>
</html>