const express = require('express');
const { MongoClient } = require('mongodb');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();

app.use(bodyParser.json());

// 提供靜態檔案服務
app.use(express.static(path.join(__dirname)));

// 身份驗證API端點
app.post('/auth', async (req, res) => {
  const { username, password, dbUri, collection } = req.body;
  
  if (!username || !password || !dbUri || !collection) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }
  
  let client;
  
  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();
    
    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);
    
    // 查詢用戶
    const user = await usersCollection.findOne({ username, password });
    
    if (!user) {
      return res.status(401).json({ success: false, message: '帳號或密碼錯誤' });
    }
    
    // 登入成功
    return res.json({
      success: true,
      user: {
        username: user.username,
        role: user.role || 'user', // 如果沒有角色，預設為user
        status: user.status || false
      }
    });
    
  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});


// 啟動服務器
const PORT = process.env.PORT || 6535;
app.listen(PORT, () => {
  console.log(`身份驗證API服務器運行在端口 ${PORT}`);
});