const express = require('express');
const { MongoClient, ObjectId } = require('mongodb');
const bodyParser = require('body-parser');
const crypto = require('crypto');

// 生成隨機UID作為使用者代號
function generateUID() {
  // 生成8位隨機字符串
  return crypto.randomBytes(4).toString('hex').toUpperCase();
}

const app = express();

app.use(bodyParser.json());

// 獲取所有用戶
// 獲取所有用戶
app.post('/users', async (req, res) => {
  const { dbUri, collection } = req.body;
  
  if (!dbUri || !collection) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }
  
  let client;
  
  try {
    client = new MongoClient(dbUri);
    await client.connect();
    
    const db = client.db();
    const usersCollection = db.collection(collection);
    
    // 查詢所有用戶，排除 password 欄位
    const users = await usersCollection.find({}, { projection: { password: 0 } }).toArray();
    
    return res.json({
      success: true,
      users
    });
    
  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 新增用戶
app.post('/add-user', async (req, res) => {
  const { dbUri, collection, username, password, role, userCode, systemCode, status } = req.body;
  
  if (!dbUri || !collection || !username || !password || !role) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }
  
  let client;
  
  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();
    
    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);
    
    // 檢查用戶名是否已存在
    const existingUser = await usersCollection.findOne({ username });
    if (existingUser) {
      return res.status(400).json({ success: false, message: '用戶名已存在' });
    }

    // 生成隨機使用者代號 (如果未提供)
    const generatedUserCode = userCode || generateUID();

    // 檢查使用者代號是否已存在（如果有提供的話）
    if (userCode) {
      const existingUserCode = await usersCollection.findOne({ userCode });
      if (existingUserCode) {
        return res.status(400).json({ success: false, message: '使用者代號（員工編號）已存在' });
      }
    }
    
    // 新增用戶
    const result = await usersCollection.insertOne({
      username,
      password,
      role,
      userCode: generatedUserCode,
      systemCode: systemCode || 'ZZ999',
      status: status !== undefined ? status : true, // 預設為啟用
      createdAt: new Date()
    });
    
    return res.json({
      success: true,
      message: '用戶新增成功'
    });
    
  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 刪除用戶
app.post('/delete-user', async (req, res) => {
  const { dbUri, collection, username } = req.body;
  
  if (!dbUri || !collection || !username) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }
  
  let client;
  
  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();
    
    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);
    
    // 刪除用戶
    const result = await usersCollection.deleteOne({ username });
    
    if (result.deletedCount === 0) {
      return res.status(404).json({ success: false, message: '用戶不存在' });
    }
    
    return res.json({
      success: true,
      message: '用戶刪除成功'
    });
    
  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 更新用戶狀態
app.post('/update-user-status', async (req, res) => {
  const { dbUri, collection, username, status } = req.body;

  if (!dbUri || !collection || !username || status === undefined) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }

  let client;

  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();

    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);

    // 檢查用戶是否存在
    const existingUser = await usersCollection.findOne({ username });
    if (!existingUser) {
      return res.status(404).json({ success: false, message: '用戶不存在' });
    }

    // 更新用戶狀態
    const result = await usersCollection.updateOne(
      { username },
      { $set: { status: status } }
    );

    if (result.modifiedCount === 0) {
      return res.status(400).json({ success: false, message: '用戶狀態更新失敗' });
    }

    return res.json({
      success: true,
      message: `用戶狀態已${status ? '啟用' : '停用'}`
    });

  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 更新用戶角色
app.post('/update-user-role', async (req, res) => {
  const { dbUri, collection, username, role } = req.body;

  if (!dbUri || !collection || !username || !role) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }

  // 驗證角色值
  if (!['admin', 'user'].includes(role)) {
    return res.status(400).json({ success: false, message: '無效的角色值' });
  }

  let client;

  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();

    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);

    // 檢查用戶是否存在
    const existingUser = await usersCollection.findOne({ username });
    if (!existingUser) {
      return res.status(404).json({ success: false, message: '用戶不存在' });
    }

    // 更新用戶角色
    const result = await usersCollection.updateOne(
      { username },
      {
        $set: {
          role: role,
          roleUpdatedAt: new Date()
        }
      }
    );

    if (result.modifiedCount === 0) {
      return res.status(400).json({ success: false, message: '用戶角色更新失敗' });
    }

    return res.json({
      success: true,
      message: `用戶角色已更新為${role === 'admin' ? '管理員' : '一般用戶'}`
    });

  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 更改密碼
app.post('/change-password', async (req, res) => {
  const { dbUri, collection, username, currentPassword, newPassword } = req.body;

  if (!dbUri || !collection || !username || !currentPassword || !newPassword) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }

  // 密碼強度驗證
  if (newPassword.length < 6) {
    return res.status(400).json({ success: false, message: '新密碼長度至少需要6個字符' });
  }

  if (currentPassword === newPassword) {
    return res.status(400).json({ success: false, message: '新密碼不能與當前密碼相同' });
  }

  let client;

  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();

    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);

    // 驗證當前密碼
    const user = await usersCollection.findOne({ username, password: currentPassword });
    if (!user) {
      return res.status(401).json({ success: false, message: '當前密碼錯誤' });
    }

    // 更新密碼
    const result = await usersCollection.updateOne(
      { username },
      {
        $set: {
          password: newPassword,
          passwordChangedAt: new Date()
        }
      }
    );

    if (result.modifiedCount === 0) {
      return res.status(400).json({ success: false, message: '密碼更新失敗' });
    }

    return res.json({
      success: true,
      message: '密碼更改成功'
    });

  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 管理員重置用戶密碼
app.post('/reset-password', async (req, res) => {
  const { dbUri, collection, adminUsername, targetUsername, newPassword } = req.body;

  if (!dbUri || !collection || !adminUsername || !targetUsername || !newPassword) {
    return res.status(400).json({ success: false, message: '缺少必要參數' });
  }

  // 密碼強度驗證
  if (newPassword.length < 6) {
    return res.status(400).json({ success: false, message: '新密碼長度至少需要6個字符' });
  }

  let client;

  try {
    // 連接到MongoDB
    client = new MongoClient(dbUri);
    await client.connect();

    // 選擇集合
    const db = client.db();
    const usersCollection = db.collection(collection);

    // 驗證執行重置的用戶是管理員
    const adminUser = await usersCollection.findOne({ username: adminUsername });
    if (!adminUser || adminUser.role !== 'admin') {
      return res.status(403).json({ success: false, message: '權限不足，只有管理員可以重置密碼' });
    }

    // 檢查目標用戶是否存在
    const targetUser = await usersCollection.findOne({ username: targetUsername });
    if (!targetUser) {
      return res.status(404).json({ success: false, message: '目標用戶不存在' });
    }

    // 更新目標用戶密碼
    const result = await usersCollection.updateOne(
      { username: targetUsername },
      {
        $set: {
          password: newPassword,
          passwordResetAt: new Date(),
          passwordResetBy: adminUsername
        }
      }
    );

    if (result.modifiedCount === 0) {
      return res.status(400).json({ success: false, message: '密碼重置失敗' });
    }

    return res.json({
      success: true,
      message: `用戶 ${targetUsername} 的密碼已成功重置`
    });

  } catch (error) {
    console.error('MongoDB連接錯誤:', error);
    return res.status(500).json({ success: false, message: '伺服器錯誤，請稍後再試' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// 啟動服務器
const PORT = process.env.PORT || 6536;
app.listen(PORT, () => {
  console.log(`用戶管理API服務器運行在端口 ${PORT}`);
});