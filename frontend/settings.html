<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多圖辨識編輯器 - 設定</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+HK:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="js/logger.js"></script>
  <style>
    :root {
      --primary-color: #3498db;
      --primary-dark: #2980b9;
      --secondary-color: #2ecc71;
      --secondary-dark: #27ae60;
      --danger-color: #e74c3c;
      --danger-dark: #c0392b;
      --text-color: #333;
      --light-bg: #f8f9fa;
      --border-color: #e0e0e0;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --radius: 8px;
      --transition: all 0.3s ease;
    }
    
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Noto Sans HK', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
      color: var(--text-color);
      min-height: 100vh;
    }
    
    .container {
      max-width: 1000px;
      margin: 0 auto;
      background-color: white;
      border-radius: var(--radius);
      padding: 30px;
      box-shadow: var(--shadow);
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
    }
    
    .header h1 {
      margin: 0;
      color: var(--primary-dark);
      font-size: 24px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .nav-buttons {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: var(--radius);
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }
    
    .btn-primary:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }
    
    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }
    
    .btn-danger:hover {
      background-color: var(--danger-dark);
      transform: translateY(-2px);
    }
    
    .settings-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      background-color: var(--light-bg);
    }
    
    .settings-section h2 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      color: var(--primary-dark);
      border-bottom: 2px solid var(--primary-color);
      padding-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .form-group input, .form-group select {
      width: 100%;
      padding: 10px 15px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      font-family: inherit;
      font-size: 16px;
    }
    
    .form-group input:focus, .form-group select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
    
    .user-list {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    
    .user-list th, .user-list td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }
    
    .user-list th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }
    
    .user-list tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    .user-list tr:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .user-actions {
      display: flex;
      gap: 8px;
    }
    
    .btn-sm {
      padding: 5px 10px;
      font-size: 14px;
    }

    .btn-warning {
      background-color: #f39c12;
      color: white;
      border: 1px solid #e67e22;
    }

    .btn-warning:hover {
      background-color: #e67e22;
      border-color: #d35400;
    }
    
    .user-role {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .role-admin {
      background-color: #e8f5e8;
      color: var(--secondary-dark);
    }
    
    .role-user {
      background-color: #e8f4f8;
      color: var(--primary-dark);
    }
    
    .message {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 25px;
      border-radius: var(--radius);
      color: white;
      font-weight: bold;
      z-index: 1000;
      transform: translateX(100%);
      transition: transform 0.3s ease, opacity 0.3s ease;
      box-shadow: var(--shadow);
      opacity: 0;
    }
    
    .message.success {
      background-color: var(--secondary-color);
    }
    
    .message.error {
      background-color: var(--danger-color);
    }

    /* 更新 CSS 樣式，增強載入狀態的視覺效果 */
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      display: inline-block;
    }

    /* 按鈕載入狀態樣式 */
    .btn-loading {
      opacity: 0.7;
      cursor: not-allowed !important;
      pointer-events: none;
      position: relative;
    }

    .btn-loading i {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 載入中訊息樣式 */
    .message.loading {
      background-color: #3498db;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .message.show {
      transform: translateX(0);
      opacity: 1;
    }
    
    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
      }
      
      .nav-buttons {
        width: 100%;
      }
      
      .btn {
        flex: 1;
        justify-content: center;
      }
    }

    /* Checkbox 樣式 */
    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin-top: 10px;
      padding: 15px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      background-color: #f8f9fa;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-item input[type="checkbox"] {
      width: 16px;
      height: 16px;
      margin: 0;
    }

    .checkbox-item label {
      margin: 0;
      cursor: pointer;
      font-size: 14px;
      user-select: none;
    }

    .checkbox-item:first-child {
      grid-column: 1 / -1;
      font-weight: bold;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 10px;
      margin-bottom: 5px;
    }

    .checkbox-item:first-child label {
      font-weight: bold;
      color: var(--primary-color);
    }

    /* 模態視窗樣式 */
    .modal {
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .modal-content {
      background-color: white;
      border-radius: var(--radius);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      padding: 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h3 {
      margin: 0;
      color: var(--primary-dark);
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .modal-header .close {
      font-size: 24px;
      font-weight: bold;
      cursor: pointer;
      color: #999;
      transition: color 0.3s;
    }

    .modal-header .close:hover {
      color: var(--danger-color);
    }

    .modal-body {
      padding: 20px;
    }

    .modal-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 20px;
    }

    .btn-warning {
      background-color: #f39c12;
      color: white;
    }

    .btn-warning:hover {
      background-color: #e67e22;
      transform: translateY(-2px);
    }


  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-cog"></i> 系統設定</h1>
      <div class="nav-buttons">
        <button class="btn btn-primary" onclick="window.location.href='index.html'">
          <i class="fas fa-home"></i> 返回主頁
        </button>
        <button class="btn btn-danger" id="logoutBtn">
          <i class="fas fa-sign-out-alt"></i> 登出
        </button>
      </div>
    </div>
    
    <div class="settings-section">
      <h2><i class="fas fa-users"></i> 用戶管理</h2>
      <div style="margin-bottom: 15px; display: flex; justify-content: flex-end;">
        <button id="exportUsersBtn" class="btn btn-primary">
          <i class="fas fa-file-csv"></i> 匯出帳號資訊
        </button>
      </div>
      <div id="userManagement">
        <table class="user-list">
          <thead>
            <tr>
              <th>用戶名</th>
              <th>使用者代號</th>
              <th>系統代號</th>
              <th>角色</th>
              <th>狀態</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="userTableBody">
            <!-- 用戶列表將通過JavaScript動態生成 -->
          </tbody>
        </table>
      </div>
    </div>

    <div class="settings-section">
      <h2><i class="fas fa-key"></i> 更改密碼</h2>
      <form id="changePasswordForm">
        <div class="form-group">
          <label for="currentPassword">當前密碼</label>
          <input type="password" id="currentPassword" required>
        </div>
        <div class="form-group">
          <label for="newPassword">新密碼</label>
          <input type="password" id="newPassword" required minlength="6">
          <small style="color: #666; display: block; margin-top: 5px;">密碼長度至少6個字符</small>
        </div>
        <div class="form-group">
          <label for="confirmPassword">確認新密碼</label>
          <input type="password" id="confirmPassword" required>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> 更改密碼
        </button>
      </form>
    </div>

    <div class="settings-section">
      <h2><i class="fas fa-user-plus"></i> 新增用戶</h2>
      <form id="addUserForm">
        <div class="form-group">
          <label for="newUsername">用戶名</label>
          <input type="text" id="newUsername" required>
        </div>
        <div class="form-group">
          <label for="newUserCode">使用者代號</label>
          <input type="text" id="newUserCode" placeholder="請輸入員工編號" required>
          <small style="color: #666; display: block; margin-top: 5px;">請輸入對應帳號的同仁員工編號</small>
        </div>
        <!-- 隱藏欄位，不顯示給使用者 -->
        <input type="hidden" id="newSystemCode" value="ZZ999">
        <div class="form-group">
          <label for="addUserPassword">密碼</label>
          <input type="password" id="addUserPassword" required>
        </div>
        <div class="form-group">
          <label for="newRole">角色</label>
          <select id="newRole" required>
            <option value="user">一般用戶</option>
            <option value="admin">管理員</option>
          </select>
        </div>
        <div class="form-group">
          <label for="newStatus">使用者狀態</label>
          <select id="newStatus" required>
            <option value="true">啟用</option>
            <option value="false">停用</option>
          </select>
          <small style="color: #666; display: block; margin-top: 5px;">停用狀態的用戶將無法登入系統</small>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-plus"></i> 新增用戶
        </button>
      </form>
    </div>
    
    <div class="settings-section">
      <h2><i class="fas fa-history"></i> 系統日誌</h2>
      <div id="logManagement">
        <div class="form-group">
          <label for="logStartDate">開始日期</label>
          <input type="date" id="logStartDate">
        </div>
        <div class="form-group">
          <label for="logEndDate">結束日期</label>
          <input type="date" id="logEndDate">
        </div>
        <div class="form-group">
          <label>事件類型</label>
          <div class="checkbox-group">
            <div class="checkbox-item">
              <input type="checkbox" id="logActionAll" checked>
              <label for="logActionAll">全部</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionLogin" class="action-checkbox" value="login" checked>
              <label for="logActionLogin">登入</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionLogout" class="action-checkbox" value="logout" checked>
              <label for="logActionLogout">登出</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionUploadTaiex" class="action-checkbox" value="upload_taiex" checked>
              <label for="logActionUploadTaiex">上傳台指文字檔</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionUploadUs" class="action-checkbox" value="upload_us" checked>
              <label for="logActionUploadUs">上傳美股文字檔</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionAddUser" class="action-checkbox" value="add_user" checked>
              <label for="logActionAddUser">新增用戶</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionDeleteUser" class="action-checkbox" value="delete_user" checked>
              <label for="logActionDeleteUser">刪除用戶</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionUpdateUserStatus" class="action-checkbox" value="update_user_status" checked>
              <label for="logActionUpdateUserStatus">更新用戶狀態</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionUpdateUserRole" class="action-checkbox" value="update_user_role" checked>
              <label for="logActionUpdateUserRole">修改用戶角色</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionExportUsers" class="action-checkbox" value="export_users" checked>
              <label for="logActionExportUsers">匯出帳號資訊</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionQueryLogs" class="action-checkbox" value="query_logs" checked>
              <label for="logActionQueryLogs">查詢日誌</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionExportLogs" class="action-checkbox" value="export_logs" checked>
              <label for="logActionExportLogs">導出日誌</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionChangePassword" class="action-checkbox" value="change_password" checked>
              <label for="logActionChangePassword">更改密碼</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="logActionResetPassword" class="action-checkbox" value="reset_password" checked>
              <label for="logActionResetPassword">重置密碼</label>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label for="logUsername">用戶名</label>
          <input type="text" id="logUsername" placeholder="輸入用戶名過濾">
        </div>
        <div style="display: flex; gap: 10px;">
          <button class="btn btn-primary" id="searchLogsBtn">
            <i class="fas fa-search"></i> 查詢日誌
          </button>
          <button class="btn btn-primary" id="exportLogsBtn">
            <i class="fas fa-file-export"></i> 導出日誌
          </button>
        </div>
        
        <div style="margin-top: 20px; max-height: 400px; overflow-y: auto;">
          <table class="user-list" id="logTable">
            <thead>
              <tr>
                <th>時間</th>
                <th>用戶</th>
                <th>事件</th>
                <th>詳細資訊</th>
              </tr>
            </thead>
            <tbody id="logTableBody">
              <!-- 日誌列表將通過JavaScript動態生成 -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- 重置密碼模態視窗 -->
  <div id="resetPasswordModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-key"></i> 重置用戶密碼</h3>
        <span class="close" onclick="closeResetPasswordModal()">&times;</span>
      </div>
      <div class="modal-body">
        <p>正在重置用戶 <strong id="resetTargetUsername"></strong> 的密碼</p>
        <form id="resetPasswordForm">
          <div class="form-group">
            <label for="resetNewPassword">新密碼</label>
            <input type="password" id="resetNewPassword" required minlength="6">
            <small style="color: #666; display: block; margin-top: 5px;">密碼長度至少6個字符</small>
          </div>
          <div class="form-group">
            <label for="resetConfirmPassword">確認新密碼</label>
            <input type="password" id="resetConfirmPassword" required>
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" onclick="closeResetPasswordModal()">取消</button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> 重置密碼
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- 修改角色模態視窗 -->
  <div id="changeRoleModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-user-cog"></i> 修改用戶角色</h3>
        <span class="close" onclick="closeChangeRoleModal()">&times;</span>
      </div>
      <div class="modal-body">
        <p>正在修改用戶 <strong id="changeRoleTargetUsername"></strong> 的角色</p>
        <form id="changeRoleForm">
          <div class="form-group">
            <label for="newUserRole">新角色</label>
            <select id="newUserRole" required>
              <option value="user">一般用戶</option>
              <option value="admin">管理員</option>
            </select>
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" onclick="closeChangeRoleModal()">取消</button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> 修改角色
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script>
    // 檢查用戶是否已登入，以及是否為管理員
    const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
    
    if (!currentUser) {
      // 未登入，重定向到登入頁面
      window.location.href = 'login.html';
    } else if (currentUser.role !== 'admin') {
      // 非管理員，重定向到主頁
      window.location.href = 'index.html';
      alert('您沒有權限訪問此頁面');
    }
    
    // 登出功能
    document.getElementById('logoutBtn').addEventListener('click', async () => {
      // 記錄登出事件
      try {
        if (window.Logger) {
          await Logger.logLogout();
        }
      } catch (logError) {
        console.error(logError);
      }
      
      sessionStorage.removeItem('loginStatus');
      window.location.href = 'login.html';
    });
    
    // 顯示訊息函數
    function showMessage(text, type) {
      const existingMessage = document.querySelector('.message');
      if (existingMessage) {
        existingMessage.remove();
      }

      const message = document.createElement('div');
      message.className = `message ${type}`;
      message.textContent = text;
      document.body.appendChild(message);

      setTimeout(() => {
        message.classList.add('show');
      }, 100);

      setTimeout(() => {
        message.classList.remove('show');
        setTimeout(() => {
          if (message.parentNode) {
            message.remove();
          }
        }, 300);
      }, 3000);
    }

    // 顯示 loading 訊息函數
    function showLoadingMessage(text) {
      const existingMessage = document.querySelector('.message');
      if (existingMessage) {
        existingMessage.remove();
      }

      const message = document.createElement('div');
      message.className = 'message loading';
      message.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <div class="loading-spinner"></div>
          <span>${text}</span>
        </div>
      `;
      document.body.appendChild(message);

      setTimeout(() => {
        message.classList.add('show');
      }, 100);

      return message; // 返回 message 元素，以便後續移除
    }

    // 隱藏 loading 訊息函數
    function hideLoadingMessage() {
      const loadingMessage = document.querySelector('.message.loading');
      if (loadingMessage) {
        loadingMessage.classList.remove('show');
        setTimeout(() => {
          if (loadingMessage.parentNode) {
            loadingMessage.remove();
          }
        }, 300);
      }
    }

    // 設置按鈕載入狀態
    function setButtonLoading(button, isLoading, originalIcon = 'fas fa-spinner') {
      if (isLoading) {
        button.classList.add('btn-loading');
        button.disabled = true;
        const icon = button.querySelector('i');
        if (icon) {
          icon.className = 'fas fa-spinner';
        }
      } else {
        button.classList.remove('btn-loading');
        button.disabled = false;
        const icon = button.querySelector('i');
        if (icon) {
          icon.className = originalIcon;
        }
      }
    }
    
    // 載入用戶列表
    async function loadUsers() {
      try {
        // 嘗試連接API服務器
        let response;
        
        try {
          // 嘗試連接本地API服務器
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/users', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack"
            })
          });
        } catch (localError) {
          console.error(localError);
          
          // 顯示錯誤訊息
          showMessage('無法連接到API服務器', 'error');
          throw localError; // 中止操作，避免再次嘗試相同的請求
        }
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          const userTableBody = document.getElementById('userTableBody');
          userTableBody.innerHTML = '';
          
          // 獲取當前登入用戶
          const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
          
          result.users.forEach(user => {
            const row = document.createElement('tr');
            
            // 是否為當前登入用戶
            const isCurrentUser = user.username === currentUser.username;
            
            // 如果是當前用戶，增加特殊樣式
            if (isCurrentUser) {
              row.style.backgroundColor = '#f0f8ff'; // 淺藍色背景，突出顯示當前用戶
              row.style.position = 'relative';
            }
            
            // 用戶名
            const usernameCell = document.createElement('td');
            if (isCurrentUser) {
              // 用一個更醒目的標籤顯示當前用戶
              usernameCell.innerHTML = `
                <div style="position: relative;">
                  <strong>${user.username}</strong>
                  <span style="position: absolute; top: -10px; right: -10px; background-color: #4CAF50; color: white; font-size: 10px; padding: 2px 6px; border-radius: 10px;">當前用戶</span>
                </div>
              `;
            } else {
              usernameCell.textContent = user.username;
            }
            
            // 使用者代號
            const userCodeCell = document.createElement('td');
            userCodeCell.textContent = user.userCode || '-';
            
            // 系統代號
            const systemCodeCell = document.createElement('td');
            systemCodeCell.textContent = user.systemCode || 'ZZ999';
            
            // 角色
            const roleCell = document.createElement('td');
            const roleSpan = document.createElement('span');
            roleSpan.className = `user-role role-${user.role}`;
            roleSpan.textContent = user.role === 'admin' ? '管理員' : '一般用戶';
            roleCell.appendChild(roleSpan);
            
            // 狀態
            const statusCell = document.createElement('td');
            const statusBadge = document.createElement('span');
            const isActive = user.status !== false; // 如果status不存在或為true，則視為啟用
            statusBadge.className = `user-role ${isActive ? 'role-admin' : 'role-user'}`;
            statusBadge.style.backgroundColor = isActive ? '#e8f5e8' : '#ffebee';
            statusBadge.style.color = isActive ? '#27ae60' : '#e74c3c';
            statusBadge.textContent = isActive ? '啟用' : '停用';
            statusCell.appendChild(statusBadge);
            
            // 操作按鈕
            const actionsCell = document.createElement('td');
            actionsCell.className = 'user-actions';

            // 修改角色按鈕
            const changeRoleBtn = document.createElement('button');
            changeRoleBtn.className = 'btn btn-sm btn-warning';
            changeRoleBtn.innerHTML = '<i class="fas fa-user-cog"></i>';
            changeRoleBtn.title = '修改角色';
            changeRoleBtn.onclick = () => showChangeRoleModal(user.username, user.role);
            actionsCell.appendChild(changeRoleBtn);

            // 更新狀態按鈕
            const toggleStatusBtn = document.createElement('button');
            
            if (isCurrentUser && isActive) {
              // 當前用戶不允許停用自己
              toggleStatusBtn.className = 'btn btn-secondary btn-sm';
              toggleStatusBtn.innerHTML = '<i class="fas fa-user-slash"></i>';
              toggleStatusBtn.title = '不能停用自己的帳號';
              toggleStatusBtn.disabled = true;
              toggleStatusBtn.style.opacity = '0.6';
              toggleStatusBtn.style.cursor = 'not-allowed';
              toggleStatusBtn.setAttribute('data-tooltip', '不能停用自己的帳號');

              // 添加點擊事件，當嘗試點擊禁用按鈕時顯示提示
              toggleStatusBtn.addEventListener('click', (e) => {
                e.preventDefault();
                showMessage('系統安全限制：不能停用自己當前登入的帳號', 'error');
              });
            } else {
              toggleStatusBtn.className = `btn ${isActive ? 'btn-danger' : 'btn-primary'} btn-sm`;
              toggleStatusBtn.innerHTML = isActive ?
                '<i class="fas fa-user-slash"></i>' :
                '<i class="fas fa-user-check"></i>';
              toggleStatusBtn.title = isActive ? '停用用戶' : '啟用用戶';
              toggleStatusBtn.addEventListener('click', () => updateUserStatus(user.username, !isActive));
            }

            // 重置密碼按鈕（只有管理員可以看到，且不能重置自己的密碼）
            const resetPasswordBtn = document.createElement('button');
            if (currentUser.role === 'admin' && !isCurrentUser) {
              resetPasswordBtn.className = 'btn btn-warning btn-sm';
              resetPasswordBtn.innerHTML = '<i class="fas fa-key"></i>';
              resetPasswordBtn.title = '重置密碼';
              resetPasswordBtn.addEventListener('click', () => showResetPasswordModal(user.username));
            } else {
              // 如果不是管理員或是當前用戶，不顯示重置密碼按鈕
              resetPasswordBtn.style.display = 'none';
            }

            // 刪除按鈕
            const deleteBtn = document.createElement('button');
            
            if (isCurrentUser) {
              // 當前用戶不允許刪除自己
              deleteBtn.className = 'btn btn-secondary btn-sm';
              deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
              deleteBtn.title = '不能刪除自己的帳號';
              deleteBtn.disabled = true;
              deleteBtn.style.opacity = '0.6';
              deleteBtn.style.cursor = 'not-allowed';

              // 添加點擊事件，當嘗試點擊刪除按鈕時顯示提示
              deleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                showMessage('系統安全限制：不能刪除自己當前登入的帳號', 'error');
              });
            } else {
              deleteBtn.className = 'btn btn-danger btn-sm';
              deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
              deleteBtn.title = '刪除用戶';
              deleteBtn.addEventListener('click', () => deleteUser(user.username));
            }
            
            actionsCell.appendChild(toggleStatusBtn);
            if (currentUser.role === 'admin' && !isCurrentUser) {
              actionsCell.appendChild(resetPasswordBtn);
            }
            actionsCell.appendChild(deleteBtn);
            
            row.appendChild(usernameCell);
            row.appendChild(userCodeCell);
            row.appendChild(systemCodeCell);
            row.appendChild(roleCell);
            row.appendChild(statusCell);
            row.appendChild(actionsCell);
            
            userTableBody.appendChild(row);
          });
        } else {
          showMessage('載入用戶列表失敗', 'error');
        }
      } catch (error) {
        console.error(error);
        showMessage('載入用戶列表失敗', 'error');
      }
    }
    
    // 新增用戶
    document.getElementById('addUserForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      const username = document.getElementById('newUsername').value.trim();
      const userCode = document.getElementById('newUserCode').value.trim(); // 使用者輸入的員工編號
      const systemCode = document.getElementById('newSystemCode').value.trim() || 'ZZ999';
      const password = document.getElementById('addUserPassword').value.trim(); // 加入 trim() 去除空白
      const role = document.getElementById('newRole').value;
      const status = document.getElementById('newStatus').value === 'true';

      // 加入除錯資訊
      console.log('新增用戶表單資料:', {
        username: username,
        userCode: userCode,
        systemCode: systemCode,
        password: password ? '***已填寫***' : '***未填寫***',
        role: role,
        status: status
      });

      // 驗證必填欄位
      if (!username) {
        showMessage('請輸入用戶名', 'error');
        return;
      }

      if (!userCode) {
        showMessage('請輸入使用者代號（員工編號）', 'error');
        return;
      }

      if (!password) {
        showMessage('請輸入密碼', 'error');
        return;
      }

      // 密碼長度驗證
      if (password.length < 6) {
        showMessage('密碼長度至少需要6個字符', 'error');
        return;
      }

      // 獲取提交按鈕並設置載入狀態
      const submitBtn = e.target.querySelector('button[type="submit"]');
      setButtonLoading(submitBtn, true, 'fas fa-plus');

      // 顯示 loading 提示
      const loadingMsg = showLoadingMessage('正在新增用戶...');

      try {
        let response;
        
        try {
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/add-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              username,
              userCode,
              systemCode,
              password,
              role,
              status
            })
          });
        } catch (localError) {
          console.error(localError);
          
          // 顯示錯誤訊息
          hideLoadingMessage();
          showMessage('無法連接到API服務器', 'error');
          setButtonLoading(submitBtn, false, 'fas fa-plus');
          throw localError; // 中止操作，避免再次嘗試相同的請求
        }
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          // 記錄新增用戶事件
          try {
            if (window.Logger) {
              await Logger.logAddUser(username, role);
            }
          } catch (logError) {
            console.error(logError);
          }
          
          hideLoadingMessage();
          setButtonLoading(submitBtn, false, 'fas fa-plus');
          showMessage('用戶新增成功', 'success');
          document.getElementById('addUserForm').reset();
          loadUsers();
        } else {
          hideLoadingMessage();
          setButtonLoading(submitBtn, false, 'fas fa-plus');
          showMessage(result.message || '用戶新增失敗', 'error');
        }
      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        setButtonLoading(submitBtn, false, 'fas fa-plus');
        showMessage('新增用戶失敗', 'error');
      }
    });

    // 更改密碼
    document.getElementById('changePasswordForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      const currentPassword = document.getElementById('currentPassword').value;
      const newPassword = document.getElementById('newPassword').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // 驗證新密碼
      if (newPassword.length < 6) {
        showMessage('新密碼長度至少需要6個字符', 'error');
        return;
      }

      if (newPassword !== confirmPassword) {
        showMessage('新密碼與確認密碼不一致', 'error');
        return;
      }

      if (currentPassword === newPassword) {
        showMessage('新密碼不能與當前密碼相同', 'error');
        return;
      }

      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      if (!currentUser) {
        showMessage('用戶未登入', 'error');
        return;
      }

      const submitBtn = e.target.querySelector('button[type="submit"]');
      setButtonLoading(submitBtn, true, 'fas fa-save');

      const loadingMsg = showLoadingMessage('正在更改密碼...');

      try {
        let response;

        try {
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/change-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              username: currentUser.username,
              currentPassword,
              newPassword
            })
          });
        } catch (localError) {
          console.error(localError);

          hideLoadingMessage();
          showMessage('無法連接到API服務器', 'error');
          setButtonLoading(submitBtn, false, 'fas fa-save');
          throw localError;
        }

        const result = await response.json();
        hideLoadingMessage();

        if (response.ok && result.success) {
          // 記錄更改密碼事件
          try {
            if (window.Logger && typeof Logger.logChangePassword === 'function') {
              await Logger.logChangePassword();
            }
          } catch (logError) {
            console.error('記錄更改密碼事件失敗:', logError);
          }

          showMessage('密碼更改成功！請重新登入', 'success');

          // 清空表單
          document.getElementById('changePasswordForm').reset();

          // 3秒後自動登出
          setTimeout(() => {
            sessionStorage.removeItem('loginStatus');
            window.location.href = 'login.html';
          }, 3000);
        } else {
          showMessage(result.message || '密碼更改失敗', 'error');
        }

      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        showMessage('密碼更改失敗', 'error');
      } finally {
        setButtonLoading(submitBtn, false, 'fas fa-save');
      }
    });

    // 刪除用戶
    async function deleteUser(username) {
      // 檢查是否嘗試刪除自己的帳號
      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      if (username === currentUser.username) {
        showMessage('不能刪除自己當前登入的帳號', 'error');
        return;
      }

      if (!confirm(`確定要刪除用戶 ${username} 嗎？`)) {
        return;
      }

      // 找到對應的刪除按鈕
      const deleteBtn = event.target.closest('button');
      setButtonLoading(deleteBtn, true, 'fas fa-trash');

      // 顯示 loading 提示
      const loadingMsg = showLoadingMessage(`正在刪除用戶 ${username}...`);

      try {
        let response;
        
        try {
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/delete-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              username
            })
          });
        } catch (localError) {
          console.error(localError);
          
          // 顯示錯誤訊息
          hideLoadingMessage();
          showMessage('無法連接到API服務器', 'error');
          setButtonLoading(deleteBtn, false, 'fas fa-trash');
          throw localError; // 中止操作，避免再次嘗試相同的請求
        }
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          // 記錄刪除用戶事件
          try {
            if (window.Logger) {
              await Logger.logDeleteUser(username);
            }
          } catch (logError) {
            console.error(logError);
          }
          
          hideLoadingMessage();
          setButtonLoading(deleteBtn, false, 'fas fa-trash');
          showMessage('用戶刪除成功', 'success');
          loadUsers();
        } else {
          hideLoadingMessage();
          setButtonLoading(deleteBtn, false, 'fas fa-trash');
          showMessage(result.message || '用戶刪除失敗', 'error');
        }
      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        setButtonLoading(deleteBtn, false, 'fas fa-trash');
        showMessage('刪除用戶失敗', 'error');
      }
    }
    
    // 更新用戶狀態
    async function updateUserStatus(username, newStatus) {
      // 檢查是否嘗試停用自己的帳號
      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      if (username === currentUser.username && !newStatus) {
        showMessage('不能停用自己當前登入的帳號', 'error');
        return;
      }

      if (!confirm(`確定要${newStatus ? '啟用' : '停用'}用戶 ${username} 嗎？`)) {
        return;
      }

      // 找到對應的狀態切換按鈕
      const toggleBtn = event.target.closest('button');
      const originalIcon = newStatus ? 'fas fa-user-check' : 'fas fa-user-slash';
      setButtonLoading(toggleBtn, true, originalIcon);

      // 顯示 loading 提示
      const loadingMsg = showLoadingMessage(`正在${newStatus ? '啟用' : '停用'}用戶 ${username}...`);

      try {
        let response;
        
        try {
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/update-user-status', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              username,
              status: newStatus
            })
          });
        } catch (localError) {
          console.error(localError);
          
          // 顯示錯誤訊息
          hideLoadingMessage();
          showMessage('無法連接到API服務器', 'error');
          setButtonLoading(toggleBtn, false, originalIcon);
          throw localError; // 中止操作，避免再次嘗試相同的請求
        }
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          // 記錄更新用戶狀態事件
          try {
            if (window.Logger) {
              
              // 檢查logUpdateUserStatus方法是否存在
              if (typeof Logger.logUpdateUserStatus === 'function') {
                await Logger.logUpdateUserStatus(username, newStatus);
              } else {
                await Logger.logEvent('update_user_status', {
                  username,
                  status: newStatus,
                  details: newStatus ? '啟用用戶' : '停用用戶'
                });
              }
            } else {
              console.error('Logger 未定義，無法記錄更新用戶狀態事件');
            }
          } catch (logError) {
            console.error(logError);
          }
          
          hideLoadingMessage();
          setButtonLoading(toggleBtn, false, newStatus ? 'fas fa-user-slash' : 'fas fa-user-check');
          showMessage(`用戶 ${username} ${newStatus ? '啟用' : '停用'}成功`, 'success');
          loadUsers(); // 重新載入用戶列表
        } else {
          hideLoadingMessage();
          setButtonLoading(toggleBtn, false, originalIcon);
          showMessage(result.message || '更新用戶狀態失敗', 'error');
        }
      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        setButtonLoading(toggleBtn, false, originalIcon);
        showMessage('更新用戶狀態失敗', 'error');
      }
    }

    // 顯示重置密碼模態視窗
    function showResetPasswordModal(username) {
      document.getElementById('resetTargetUsername').textContent = username;
      document.getElementById('resetPasswordModal').style.display = 'flex';
      document.getElementById('resetPasswordForm').reset();
    }

    // 關閉重置密碼模態視窗
    function closeResetPasswordModal() {
      document.getElementById('resetPasswordModal').style.display = 'none';
      document.getElementById('resetPasswordForm').reset();
    }

    // 點擊模態視窗背景關閉視窗
    document.getElementById('resetPasswordModal').addEventListener('click', (e) => {
      if (e.target === document.getElementById('resetPasswordModal')) {
        closeResetPasswordModal();
      }
    });

    // 重置用戶密碼
    async function resetUserPassword(targetUsername, newPassword) {
      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      if (!currentUser || currentUser.role !== 'admin') {
        showMessage('權限不足，只有管理員可以重置密碼', 'error');
        return;
      }

      try {
        let response;

        try {
          response = await fetch('http://localhost:6536/reset-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              adminUsername: currentUser.username,
              targetUsername,
              newPassword
            })
          });
        } catch (localError) {
          // 如果本地API失敗，嘗試遠程API
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/reset-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              adminUsername: currentUser.username,
              targetUsername,
              newPassword
            })
          });
        }

        const result = await response.json();

        if (response.ok && result.success) {
          // 記錄重置密碼事件
          try {
            if (window.Logger && typeof Logger.logResetPassword === 'function') {
              await Logger.logResetPassword(targetUsername);
            }
          } catch (logError) {
            console.error('記錄重置密碼事件失敗:', logError);
            // 記錄失敗不影響主要功能
          }

          showMessage(`用戶 ${targetUsername} 的密碼已成功重置`, 'success');
          closeResetPasswordModal();
        } else {
          showMessage(result.message || '密碼重置失敗', 'error');
        }

      } catch (error) {
        console.error(error);
        showMessage('密碼重置失敗', 'error');
      }
    }

    // 重置密碼表單提交事件
    document.getElementById('resetPasswordForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      const targetUsername = document.getElementById('resetTargetUsername').textContent;
      const newPassword = document.getElementById('resetNewPassword').value;
      const confirmPassword = document.getElementById('resetConfirmPassword').value;

      // 驗證新密碼
      if (newPassword.length < 6) {
        showMessage('新密碼長度至少需要6個字符', 'error');
        return;
      }

      if (newPassword !== confirmPassword) {
        showMessage('新密碼與確認密碼不一致', 'error');
        return;
      }

      if (!confirm(`確定要重置用戶 ${targetUsername} 的密碼嗎？`)) {
        return;
      }

      const submitBtn = e.target.querySelector('button[type="submit"]');
      setButtonLoading(submitBtn, true, 'fas fa-save');

      // 顯示 loading 提示
      const loadingMsg = showLoadingMessage(`正在重置用戶 ${targetUsername} 的密碼...`);

      try {
        await resetUserPassword(targetUsername, newPassword);
      } finally {
        hideLoadingMessage();
        setButtonLoading(submitBtn, false, 'fas fa-save');
      }
    });

    // 顯示修改角色模態視窗
    function showChangeRoleModal(username, currentRole) {
      document.getElementById('changeRoleTargetUsername').textContent = username;
      document.getElementById('newUserRole').value = currentRole;
      document.getElementById('changeRoleModal').style.display = 'flex';
      document.getElementById('changeRoleForm').reset();
      document.getElementById('newUserRole').value = currentRole; // 重新設定當前角色
    }

    // 關閉修改角色模態視窗
    function closeChangeRoleModal() {
      document.getElementById('changeRoleModal').style.display = 'none';
    }

    // 修改用戶角色
    async function changeUserRole(username, newRole) {
      const submitBtn = document.querySelector('#changeRoleForm button[type="submit"]');
      setButtonLoading(submitBtn, true, 'fas fa-save');

      // 顯示 loading 提示
      const loadingMsg = showLoadingMessage(`正在修改用戶 ${username} 的角色...`);

      try {
        let response;

        try {
          response = await fetch('http://localhost:6536/update-user-role', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              username,
              role: newRole
            })
          });
        } catch (localError) {
          // 如果本地API失敗，嘗試遠程API
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/update-user-role', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack",
              username,
              role: newRole
            })
          });
        }

        const result = await response.json();

        if (response.ok && result.success) {
          // 記錄修改角色事件
          try {
            if (window.Logger) {
              await Logger.logUpdateUserRole(username, newRole);
            }
          } catch (logError) {
            console.error(logError);
          }

          showMessage(`用戶 ${username} 的角色已成功修改為${newRole === 'admin' ? '管理員' : '一般用戶'}`, 'success');
          closeChangeRoleModal();
          loadUsers(); // 重新載入用戶列表
        } else {
          showMessage(result.message || '角色修改失敗', 'error');
        }

      } catch (error) {
        console.error(error);
        showMessage('角色修改失敗', 'error');
      } finally {
        hideLoadingMessage();
        setButtonLoading(submitBtn, false, 'fas fa-save');
      }
    }

    // 修改角色表單提交事件
    document.getElementById('changeRoleForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      const targetUsername = document.getElementById('changeRoleTargetUsername').textContent;
      const newRole = document.getElementById('newUserRole').value;

      // 檢查是否嘗試修改自己的角色為一般用戶
      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      if (targetUsername === currentUser.username && newRole === 'user') {
        showMessage('不能將自己的角色修改為一般用戶', 'error');
        return;
      }

      if (!confirm(`確定要將用戶 ${targetUsername} 的角色修改為${newRole === 'admin' ? '管理員' : '一般用戶'}嗎？`)) {
        return;
      }

      await changeUserRole(targetUsername, newRole);
    });

    // 頁面載入時獲取用戶列表
    loadUsers();
    
    // 日誌查詢功能 - 使用 API 獲取日誌
    document.getElementById('searchLogsBtn').addEventListener('click', async (e) => {
      const startDate = document.getElementById('logStartDate').value.replace(/-/g, '');
      const endDate = document.getElementById('logEndDate').value.replace(/-/g, '');
      const selectedActions = getSelectedActions(); // 獲取選中的事件類型
      const username = document.getElementById('logUsername').value.trim();

      // 記錄查詢日誌事件
      try {
        if (window.Logger && typeof Logger.logQueryLogs === 'function') {
          await Logger.logQueryLogs({
            startDate,
            endDate,
            actions: selectedActions,
            username
          });
        }
      } catch (logError) {
        console.error('記錄查詢日誌事件失敗:', logError);
      }

      // 設置按鈕載入狀態
      const searchBtn = e.target.closest('button');
      setButtonLoading(searchBtn, true, 'fas fa-search');

      try {
        // 顯示 loading 提示
        const loadingMsg = showLoadingMessage('正在獲取日誌...');

        // 構建 API URL 和查詢參數
        let url = 'https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/logs';
        const params = new URLSearchParams();

        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        // 支援多選事件類型
        selectedActions.forEach(action => {
          params.append('actions', action);
        });
        if (username) params.append('username', username);
        
        // 添加查詢參數到 URL
        if (params.toString()) {
          url += '?' + params.toString();
        }
        
        // 發送 API 請求
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`API 請求失敗: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
          // 顯示日誌
          displayLogs(result.logs);
          hideLoadingMessage();
          setButtonLoading(searchBtn, false, 'fas fa-search');
          showMessage(`顯示 ${result.logs.length} 條日誌`, 'success');
        } else {
          hideLoadingMessage();
          setButtonLoading(searchBtn, false, 'fas fa-search');
          throw new Error(result.error || '獲取日誌失敗');
        }
      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        setButtonLoading(searchBtn, false, 'fas fa-search');
        showMessage(`查詢日誌失敗: ${error.message}`, 'error');
      }
    });
    
    // 顯示日誌
    function displayLogs(logs) {
      const logTableBody = document.getElementById('logTableBody');
      logTableBody.innerHTML = '';
      
      if (logs.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = 4;
        cell.textContent = '沒有符合條件的日誌';
        cell.style.textAlign = 'center';
        row.appendChild(cell);
        logTableBody.appendChild(row);
        return;
      }
      
      logs.forEach(log => {
        const row = document.createElement('tr');
        
        // 時間
        const timeCell = document.createElement('td');
        try {
          const date = new Date(log.timestamp);
          // 將日期格式化為 yyyy/MM/dd HH:mm 格式
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          timeCell.textContent = `${year}/${month}/${day} ${hours}:${minutes}`;
        } catch (e) {
          timeCell.textContent = log.timestamp || '未知時間';
        }
        
        // 用戶
        const userCell = document.createElement('td');
        userCell.textContent = log.username || '未知用戶';
        
        // 事件
        const actionCell = document.createElement('td');
        let actionText = '';
        switch (log.action) {
          case 'login':
            actionText = '登入';
            break;
          case 'logout':
            actionText = '登出';
            break;
          case 'upload_taiex':
            actionText = '上傳台指文字檔';
            break;
          case 'upload_us':
            actionText = '上傳美股文字檔';
            break;
          case 'add_user':
            actionText = '新增用戶';
            break;
          case 'delete_user':
            actionText = '刪除用戶';
            break;
          case 'update_user_status':
            actionText = '更新用戶狀態';
            break;
          case 'change_password':
            actionText = '更改密碼';
            break;
          case 'reset_password':
            actionText = '重置密碼';
            break;
          case 'update_user_role':
            actionText = '修改用戶角色';
            break;
          case 'query_logs':
            actionText = '查詢日誌';
            break;
          case 'export_logs':
            actionText = '導出日誌';
            break;
          case 'export_users':
            actionText = '匯出帳號資訊';
            break;
          default:
            actionText = log.action || '未知事件';
        }
        actionCell.textContent = actionText;
        
        // 詳細資訊
        const detailsCell = document.createElement('td');
        if (log.details) {
          if (typeof log.details === 'object') {
            if (log.action === 'upload_taiex' || log.action === 'upload_us') {
              if (log.details.files && Array.isArray(log.details.files)) {
                detailsCell.textContent = `檔案: ${log.details.files.join(', ')}`;
              } else {
                detailsCell.textContent = JSON.stringify(log.details);
              }
            } else if (log.action === 'add_user') {
              detailsCell.textContent = `用戶: ${log.details.username || '未知'}, 角色: ${log.details.role || '未知'}`;
              if (log.details.details) {
                detailsCell.textContent += `, ${log.details.details}`;
              }
            } else if (log.action === 'delete_user') {
              detailsCell.textContent = `用戶: ${log.details.username || '未知'}`;
              if (log.details.details) {
                detailsCell.textContent += `, ${log.details.details}`;
              }
            } else if (log.action === 'update_user_status') {
              detailsCell.textContent = `用戶: ${log.details.username || '未知'}, 狀態: ${log.details.status ? '啟用' : '停用'}`;
              if (log.details.details) {
                detailsCell.textContent += `, ${log.details.details}`;
              }
            } else if (log.action === 'reset_password') {
              detailsCell.textContent = `目標用戶: ${log.details.targetUsername || '未知'}`;
              if (log.details.details) {
                detailsCell.textContent += `, ${log.details.details}`;
              }
            } else if (log.action === 'change_password') {
              detailsCell.textContent = log.details.details || '用戶更改密碼';
            } else if (log.action === 'update_user_role') {
              detailsCell.textContent = `用戶: ${log.details.username || '未知'}, 角色: ${log.details.role === 'admin' ? '管理員' : '一般用戶'}`;
              if (log.details.details) {
                detailsCell.textContent += `, ${log.details.details}`;
              }
            } else {
              detailsCell.textContent = JSON.stringify(log.details);
            }
          } else {
            detailsCell.textContent = log.details;
          }
        } else {
          detailsCell.textContent = '-';
        }
        
        row.appendChild(timeCell);
        row.appendChild(userCell);
        row.appendChild(actionCell);
        row.appendChild(detailsCell);
        
        logTableBody.appendChild(row);
      });
    }
    
    // 初始化日期選擇器
    function initDatePickers() {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      
      const todayStr = `${year}-${month}-${day}`;
      
      document.getElementById('logStartDate').value = todayStr;
      document.getElementById('logEndDate').value = todayStr;
    }
    
    // 頁面載入時初始化日期選擇器
    initDatePickers();
    
    // 導出日誌功能 - 使用 API 獲取日誌（套用篩選條件）
    document.getElementById('exportLogsBtn').addEventListener('click', async () => {
      try {
        // 獲取篩選條件
        const startDate = document.getElementById('logStartDate').value.replace(/-/g, '');
        const endDate = document.getElementById('logEndDate').value.replace(/-/g, '');
        const selectedActions = getSelectedActions(); // 獲取選中的事件類型
        const username = document.getElementById('logUsername').value.trim();

        // 設置按鈕載入狀態
        const exportBtn = event.target.closest('button');
        setButtonLoading(exportBtn, true, 'fas fa-file-export');

        // 顯示 loading 提示
        const loadingMsg = showLoadingMessage('正在獲取日誌以導出...');

        // 構建 API URL 和查詢參數（使用與查詢日誌相同的邏輯）
        let url = 'https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/logs';
        const params = new URLSearchParams();

        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        // 支援多選事件類型
        selectedActions.forEach(action => {
          params.append('actions', action);
        });
        if (username) params.append('username', username);

        // 添加查詢參數到 URL
        if (params.toString()) {
          url += '?' + params.toString();
        }

        // 發送 API 請求
        const response = await fetch(url);
        
        if (!response.ok) {
          hideLoadingMessage();
          setButtonLoading(exportBtn, false, 'fas fa-file-export');
          throw new Error(`API 請求失敗: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
          hideLoadingMessage();
          setButtonLoading(exportBtn, false, 'fas fa-file-export');
          throw new Error(result.error || '獲取日誌失敗');
        }

        const logs = result.logs;
        
        if (logs.length === 0) {
          hideLoadingMessage();
          setButtonLoading(exportBtn, false, 'fas fa-file-export');
          showMessage('沒有可導出的日誌', 'error');
          return;
        }
        
        // 處理日誌時間格式
        const formattedLogs = logs.map(log => {
          try {
            // 複製日誌對象，避免修改原始數據
            const formattedLog = {...log};
            // 格式化時間戳為 yyyy/MM/dd HH:mm 格式
            if (formattedLog.timestamp) {
              const date = new Date(formattedLog.timestamp);
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hours = String(date.getHours()).padStart(2, '0');
              const minutes = String(date.getMinutes()).padStart(2, '0');
              formattedLog.timestamp = `${year}/${month}/${day} ${hours}:${minutes}`;
            }
            return formattedLog;
          } catch (e) {
            // 如果格式化失敗，返回原始日誌
            return log;
          }
        });
        
        // 創建 JSON 文件內容
        const jsonContent = JSON.stringify(formattedLogs, null, 2);
        
        // 創建 Blob 對象
        const blob = new Blob([jsonContent], { type: 'application/json' });
        
        // 創建下載鏈接
        const downloadUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        
        // 設置文件名（使用當前日期時間）
        const now = new Date();
        const fileName = `system_logs_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}.json`;
        
        link.href = downloadUrl;
        link.download = fileName;
        
        // 觸發下載
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 釋放 URL 對象
        URL.revokeObjectURL(downloadUrl);

        // 記錄導出日誌事件
        try {
          if (window.Logger && typeof Logger.logExportLogs === 'function') {
            await Logger.logExportLogs({
              count: logs.length,
              startDate,
              endDate,
              actions: selectedActions,
              username
            });
          }
        } catch (logError) {
          console.error('記錄導出日誌事件失敗:', logError);
        }

        // 構建篩選條件說明
        let filterInfo = '';
        const filters = [];
        if (startDate) filters.push(`開始日期: ${startDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')}`);
        if (endDate) filters.push(`結束日期: ${endDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')}`);
        if (selectedActions.length > 0 && selectedActions.length < 12) {
          filters.push(`事件類型: ${selectedActions.join(', ')}`);
        } else if (selectedActions.length === 12) {
          filters.push('事件類型: 全部');
        }
        if (username) filters.push(`用戶名: ${username}`);

        if (filters.length > 0) {
          filterInfo = ` (篩選條件: ${filters.join(', ')})`;
        }

        hideLoadingMessage();
        setButtonLoading(exportBtn, false, 'fas fa-file-export');
        showMessage(`成功導出 ${logs.length} 條日誌${filterInfo}`, 'success');
      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        setButtonLoading(exportBtn, false, 'fas fa-file-export');
        showMessage(`導出日誌失敗: ${error.message}`, 'error');
      }
    });
    
    // 匯出帳號密碼為CSV
    document.getElementById('exportUsersBtn').addEventListener('click', async () => {
      try {
        // 設置按鈕載入狀態
        const exportBtn = event.target.closest('button');
        setButtonLoading(exportBtn, true, 'fas fa-file-csv');

        // 顯示 loading 提示
        const loadingMsg = showLoadingMessage('正在獲取用戶資料以匯出...');
        
        // 嘗試連接API服務器
        let response;
        
        try {
          // 嘗試連接API服務器
          response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/users', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
              collection: "KG_Stack"
            })
          });
        } catch (localError) {
          console.error(localError);
          
          // 顯示錯誤訊息
          hideLoadingMessage();
          setButtonLoading(exportBtn, false, 'fas fa-file-csv');
          showMessage('無法連接到API服務器', 'error');
          throw localError; // 中止操作，避免再次嘗試相同的請求
        }
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          const users = result.users;
          
          if (users.length === 0) {
            hideLoadingMessage();
            setButtonLoading(exportBtn, false, 'fas fa-file-csv');
            showMessage('沒有可匯出的用戶資料', 'error');
            return;
          }
          
          // 創建CSV內容（移除密碼欄位）
          let csvContent = "用戶名,使用者代號,系統代號,角色,狀態\n";
          
          users.forEach(user => {
            const username = user.username || '';
            const userCode = user.userCode || '';
            const systemCode = user.systemCode || 'ZZ999';
            const role = user.role || 'user';
            const status = user.status !== false ? '啟用' : '停用';
            
            // 處理CSV中的特殊字符
            const escapeCsv = (field) => {
              if (field.includes(',') || field.includes('"') || field.includes('\n')) {
                return `"${field.replace(/"/g, '""')}"`;
              }
              return field;
            };
            
            csvContent += `${escapeCsv(username)},${escapeCsv(userCode)},${escapeCsv(systemCode)},${escapeCsv(role)},${escapeCsv(status)}\n`;
          });
          
          // 添加UTF-8 BOM標記，確保Excel正確識別UTF-8編碼
          const BOM = '\uFEFF';
          const csvContentWithBOM = BOM + csvContent;
          
          // 創建Blob對象
          const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8;' });
          
          // 創建下載鏈接
          const downloadUrl = URL.createObjectURL(blob);
          const link = document.createElement('a');
          
          // 設置文件名（使用當前日期時間）
          const now = new Date();
          const fileName = `users_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}.csv`;
          
          link.href = downloadUrl;
          link.download = fileName;
          
          // 觸發下載
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          // 釋放URL對象
          URL.revokeObjectURL(downloadUrl);

          // 記錄匯出用戶事件
          try {
            if (window.Logger && typeof Logger.logExportUsers === 'function') {
              await Logger.logExportUsers();
            }
          } catch (logError) {
            console.error('記錄匯出用戶事件失敗:', logError);
          }

          hideLoadingMessage();
          setButtonLoading(exportBtn, false, 'fas fa-file-csv');
          showMessage(`成功匯出 ${users.length} 筆用戶資料`, 'success');
        } else {
          hideLoadingMessage();
          setButtonLoading(exportBtn, false, 'fas fa-file-csv');
          showMessage('獲取用戶資料失敗', 'error');
        }
      } catch (error) {
        console.error(error);
        hideLoadingMessage();
        setButtonLoading(exportBtn, false, 'fas fa-file-csv');
        showMessage(`匯出用戶資料失敗: ${error.message}`, 'error');
      }
    });

    // Checkbox 邏輯處理
    document.addEventListener('DOMContentLoaded', function() {
      const allCheckbox = document.getElementById('logActionAll');
      const actionCheckboxes = document.querySelectorAll('.action-checkbox');

      // "全部" checkbox 邏輯
      allCheckbox.addEventListener('change', function() {
        if (this.checked) {
          // 勾選全部時，所有子項目都勾選
          actionCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
          });
        } else {
          // 取消勾選全部時，下方選項維持原狀（不改變）
          // 根據需求，這裡不做任何操作
        }
      });

      // 子項目 checkbox 邏輯
      actionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          // 檢查是否所有子項目都被勾選
          const allChecked = Array.from(actionCheckboxes).every(cb => cb.checked);

          if (allChecked) {
            // 如果所有子項目都勾選，則勾選"全部"
            allCheckbox.checked = true;
          } else {
            // 如果有任何子項目未勾選，則取消勾選"全部"
            allCheckbox.checked = false;
          }
        });
      });
    });

    // 獲取選中的事件類型
    function getSelectedActions() {
      const selectedActions = [];
      const actionCheckboxes = document.querySelectorAll('.action-checkbox:checked');

      actionCheckboxes.forEach(checkbox => {
        selectedActions.push(checkbox.value);
      });

      return selectedActions;
    }
  </script>
</body>
</html>
