const { MongoClient } = require('mongodb');

// MongoDB連接字串
const dbUri = "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot";
const collectionName = "KG_Stack";

async function initializeDatabase() {
  let client;
  
  try {
    console.log('正在連接到MongoDB...');
    client = new MongoClient(dbUri);
    await client.connect();
    console.log('成功連接到MongoDB');
    
    const db = client.db();
    
    // 檢查集合是否存在
    const collections = await db.listCollections({ name: collectionName }).toArray();
    const collectionExists = collections.length > 0;
    
    if (collectionExists) {
      console.log(`集合 ${collectionName} 已存在`);
      
      // 檢查是否有用戶
      const usersCollection = db.collection(collectionName);
      const userCount = await usersCollection.countDocuments();
      
      if (userCount > 0) {
        console.log(`集合中已有 ${userCount} 個用戶，無需初始化`);
        return;
      }
    } else {
      console.log(`創建集合 ${collectionName}`);
      await db.createCollection(collectionName);
    }
    
    // 添加預設用戶
    const usersCollection = db.collection(collectionName);
    
    console.log('添加預設用戶...');
    await usersCollection.insertMany([
      {
        username: 'admin',
        password: 'admin123',
        role: 'admin',
        createdAt: new Date()
      },
      {
        username: 'user1',
        password: 'user123',
        role: 'user',
        createdAt: new Date()
      }
    ]);
    
    console.log('預設用戶添加成功');
    console.log('資料庫初始化完成');
    
  } catch (error) {
    console.error('初始化資料庫時發生錯誤:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB連接已關閉');
    }
  }
}

// 執行初始化
initializeDatabase().catch(console.error);