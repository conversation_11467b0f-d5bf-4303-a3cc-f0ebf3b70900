from fastapi import FastAPI, File, UploadFile, Request, Response, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from docx import Document
from io import BytesIO
from LLM import OpenAiLLM
from pydantic import BaseModel, Field


def us3(docs_text, OPENAI_API_KEY):
    
    class Extract(BaseModel):
        close_data: str = Field(..., description="美股收盤的完整內容")
        important_static: str = Field(..., description="重要經濟數據的完整內容")

    instruct_prompt = """
    你是一個專業的金融分析師，請根據以下文本內容，提取美股收盤和重要經濟數據的摘要。
    文本內容如下：
    {data}
    請將美股收盤的內容提取為 close_data，重要經濟數據的內容提取為 important_static。
    回傳格式為 JSON，包含 close_data 和 important_static 兩個欄位
    並且每個欄位的內容都要完整且清晰，不可隨意更改。
    請依照XXX方面的區間來去判斷是否是該方面的內容，不要抓到其他範圍的內容。
    例如: 美股收盤的內容只抓取 美股收盤方面~ 下一個方面的資料。 重要經濟數據則是判斷下一個小標題之前的內容。 但不要抓到標題。
    """
    
    Sector = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=Extract)
    result = Sector.chat_json([
        {"role": "user", "content": instruct_prompt.format(data=docs_text) }
    ])
    
    result = result.model_dump()
    
    # 組合結果
    result = (
        f"[美股收盤]{result.get("close_data","")}\n"
        f"[重要經濟數據]{result.get("important_static","")}"
    )

    return [[{
        'type': 'text',
        'value': result,
        'x': 20,
        'y': 84,
        'fontSize': 22,
        'fill': '#00000'
    }]]


app = FastAPI()

@app.post("/upload_files/")
async def upload_files_async(files: list[UploadFile] = File(...)):
    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})
    
    key = ""
    us3(docx_text, key)
    
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=12366)