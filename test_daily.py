#!/usr/bin/env python3
"""
手動測試腳本
用於測試股市數據處理功能
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加項目路徑到 Python 路徑
sys.path.append('/home/<USER>/ai_env/sam/project/stock_img')

from daily_stock_processor import StockDataProcessor

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

async def test_api_connection():
    """測試 API 連接"""
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:4512/tasks") as response:
                if response.status == 200:
                    print("✅ API 連接正常")
                    return True
                else:
                    print(f"❌ API 連接失敗，狀態碼: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ API 連接失敗: {str(e)}")
        return False

async def test_file_processing():
    """測試文件處理"""
    processor = StockDataProcessor()
    
    print("📁 檢查數據目錄...")
    print(f"目標目錄: {processor.data_dir}")
    
    if not processor.data_dir.exists():
        print(f"❌ 數據目錄不存在: {processor.data_dir}")
        return False
    
    taiwan_files, us_files = processor.find_files()
    
    print(f"🇹🇼 台股文件: {[f.name for f in taiwan_files]}")
    print(f"🇺🇸 美股文件: {[f.name for f in us_files]}")
    
    if taiwan_files:
        txt_file, docx_file = processor.validate_files(taiwan_files)
        if txt_file and docx_file:
            print("✅ 台股文件驗證通過")
        else:
            print("❌ 台股文件驗證失敗")
    
    if us_files:
        txt_file, docx_file = processor.validate_files(us_files)
        if txt_file and docx_file:
            print("✅ 美股文件驗證通過")
        else:
            print("❌ 美股文件驗證失敗")
    
    return True

async def test_sftp_script():
    """測試 SFTP 腳本"""
    processor = StockDataProcessor()
    
    print("📡 測試 SFTP 腳本執行...")
    
    # 注意：這會實際執行 SFTP 腳本，可能會下載新數據
    # 在生產環境中要小心使用
    confirm = input("是否要執行 SFTP 腳本？這可能會下載新數據 (y/N): ")
    if confirm.lower() != 'y':
        print("⏭️ 跳過 SFTP 腳本測試")
        return True
    
    result = await processor.execute_sftp_script()
    
    if result:
        print("✅ SFTP 腳本執行成功")
    else:
        print("❌ SFTP 腳本執行失敗")
    
    return result

async def test_parallel_api_calls():
    """測試並行 API 調用"""
    processor = StockDataProcessor()
    
    print("🔄 測試並行 API 調用...")
    
    # 檢查是否有文件可供測試
    taiwan_files, us_files = processor.find_files()
    
    if not taiwan_files and not us_files:
        print("❌ 沒有找到測試文件，跳過並行測試")
        return False
    
    confirm = input("是否要測試並行 API 調用？這會同時調用台股和美股 API (y/N): ")
    if confirm.lower() != 'y':
        print("⏭️ 跳過並行 API 調用測試")
        return True
    
    # 模擬並行處理
    tasks = []
    
    if taiwan_files:
        txt_file, docx_file = processor.validate_files(taiwan_files)
        if txt_file and docx_file:
            print(f"📤 準備上傳台股文件: {txt_file.name}, {docx_file.name}")
            task = processor.process_market_data(taiwan_files, "台股", "stock_index_async")
            tasks.append(("台股", task))
    
    if us_files:
        txt_file, docx_file = processor.validate_files(us_files)
        if txt_file and docx_file:
            print(f"📤 準備上傳美股文件: {txt_file.name}, {docx_file.name}")
            task = processor.process_market_data(us_files, "美股", "uss_index_async")
            tasks.append(("美股", task))
    
    if not tasks:
        print("❌ 沒有有效的任務可以執行")
        return False
    
    print(f"🚀 開始並行執行 {len(tasks)} 個任務...")
    
    # 並行執行任務
    results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
    
    # 檢查結果
    success_count = 0
    task_ids = []
    
    for i, (market_type, _) in enumerate(tasks):
        if i < len(results):
            result = results[i]
            if isinstance(result, Exception):
                print(f"❌ {market_type} 任務執行出錯: {str(result)}")
            else:
                upload_success, task_id = result
                if upload_success and task_id:
                    print(f"✅ {market_type} 任務提交成功，task_id: {task_id}")
                    task_ids.append((market_type, task_id))
                    success_count += 1
                else:
                    print(f"❌ {market_type} 任務提交失敗")
    
    if success_count == 0:
        print("❌ 所有任務都提交失敗")
        return False
    
    print(f"📊 成功提交 {success_count}/{len(tasks)} 個任務")
    
    # 如果有任務提交成功，可以選擇等待完成
    if task_ids:
        wait_confirm = input("是否要等待任務完成？(y/N): ")
        if wait_confirm.lower() == 'y':
            print("⏳ 開始等待任務完成...")
            
            wait_tasks = []
            for market_type, task_id in task_ids:
                wait_tasks.append(processor.wait_for_task_completion(task_id, market_type))
            
            completion_results = await asyncio.gather(*wait_tasks, return_exceptions=True)
            
            completed_count = 0
            for i, (market_type, task_id) in enumerate(task_ids):
                if i < len(completion_results):
                    result = completion_results[i]
                    if isinstance(result, Exception):
                        print(f"❌ {market_type} 任務等待過程中出錯: {str(result)}")
                    elif result:
                        print(f"✅ {market_type} 任務完成成功")
                        completed_count += 1
                    else:
                        print(f"❌ {market_type} 任務完成失敗")
            
            print(f"📊 {completed_count}/{len(task_ids)} 個任務成功完成")
            return completed_count > 0
        else:
            print("⏭️ 跳過等待任務完成")
    
    return success_count > 0

async def test_timing_comparison():
    """測試並行與串行的時間比較"""
    print("⏱️ 時間比較測試...")
    
    confirm = input("是否要進行時間比較測試？這會實際調用 API (y/N): ")
    if confirm.lower() != 'y':
        print("⏭️ 跳過時間比較測試")
        return True
    
    processor = StockDataProcessor()
    taiwan_files, us_files = processor.find_files()
    
    if not taiwan_files or not us_files:
        print("❌ 需要同時有台股和美股文件才能進行比較測試")
        return False
    
    import time
    
    # 測試並行處理
    print("🏃‍♂️ 測試並行處理...")
    start_time = time.time()
    
    parallel_tasks = [
        processor.process_market_data(taiwan_files, "台股", "stock_index_async"),
        processor.process_market_data(us_files, "美股", "uss_index_async")
    ]
    
    parallel_results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
    parallel_time = time.time() - start_time
    
    print(f"📊 並行處理時間: {parallel_time:.2f} 秒")
    
    # 檢查並行結果
    parallel_success = True
    for i, result in enumerate(parallel_results):
        market = "台股" if i == 0 else "美股"
        if isinstance(result, Exception):
            print(f"❌ 並行處理 {market} 出錯: {str(result)}")
            parallel_success = False
        else:
            upload_success, task_id = result
            if not upload_success:
                print(f"❌ 並行處理 {market} 失敗")
                parallel_success = False
    
    # 等待一段時間再測試串行（避免API限制）
    print("⏳ 等待 30 秒再進行串行測試...")
    await asyncio.sleep(30)
    
    # 測試串行處理
    print("🚶‍♂️ 測試串行處理...")
    start_time = time.time()
    
    taiwan_result = await processor.process_market_data(taiwan_files, "台股", "stock_index_async")
    us_result = await processor.process_market_data(us_files, "美股", "uss_index_async")
    
    serial_time = time.time() - start_time
    
    print(f"📊 串行處理時間: {serial_time:.2f} 秒")
    
    # 比較結果
    if parallel_time < serial_time:
        time_saved = serial_time - parallel_time
        percentage_saved = (time_saved / serial_time) * 100
        print(f"✅ 並行處理節省時間: {time_saved:.2f} 秒 ({percentage_saved:.1f}%)")
    else:
        print("⚠️ 並行處理沒有明顯的時間優勢")
    
async def test_full_process():
    """測試完整流程"""
    print("🔄 測試完整流程...")
    
    confirm = input("是否要執行完整流程測試？這會調用實際的 API (y/N): ")
    if confirm.lower() != 'y':
        print("⏭️ 跳過完整流程測試")
        return True
    
    processor = StockDataProcessor()
    result = await processor.process_daily_data()
    
    if result:
        print("✅ 完整流程測試成功")
    else:
        print("❌ 完整流程測試失敗")
    
    return result

async def main():
    """主測試函數"""
    print("🧪 開始測試股市數據處理系統")
    print("=" * 50)
    
    tests = [
        ("API 連接測試", test_api_connection),
        ("文件處理測試", test_file_processing),
        ("並行 API 調用測試", test_parallel_api_calls),
        ("時間比較測試", test_timing_comparison),
        ("SFTP 腳本測試", test_sftp_script),
        ("完整流程測試", test_full_process)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 執行 {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 發生錯誤: {str(e)}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n總計: {success_count}/{total_count} 項測試通過")
    
    if success_count == total_count:
        print("🎉 所有測試都通過了！")
    else:
        print("⚠️ 部分測試失敗，請檢查日誌")

if __name__ == "__main__":
    asyncio.run(main())