from typing import Type, List, Dict, Any
from pydantic import BaseModel
from openai import OpenAI

class OpenAiLLM:
    def __init__(self, model_name: str, api_key: str, response_model: Type[BaseModel] = None):
        self.model_name = model_name
        self.response_model = response_model
        self.client = OpenAI(api_key=api_key)

    def chat_json(self, messages: List[Dict[str, Any]]) -> BaseModel:
        completion = self.client.beta.chat.completions.parse(
            model=self.model_name,
            messages=messages,
            response_format=self.response_model,
        )
        return completion.choices[0].message.parsed
    
    
    def chat(self, messages: List[Dict[str, Any]]) -> str:
        completion = self.client.chat.completions.create(
            model=self.model_name,
            messages=messages
        )
        return completion.choices[0].message.content