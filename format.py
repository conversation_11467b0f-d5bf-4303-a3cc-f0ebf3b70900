from pydantic import BaseModel, Field
from datetime import date
from typing import List, Optional

class IndexData(BaseModel):
    current_value: float = Field(..., description="報告當日指數值")
    point_change: float = Field(..., description="報告當日與前一日的點數變化")
    point_trend: str = Field(..., description="點數變化趨勢，可能值：up 或 down")
    percent_change: float = Field(..., description="報告當日與前一日的百分比變化")
    percent_trend: str = Field(..., description="百分比變化趨勢，可能值：up 或 down")

class StockIndex(BaseModel):
    report_date: str = Field(..., description="報告日期，格式為 MM/DD")
    title: str = Field(..., description="報告標題")
    previous_date: str = Field(..., description="昨日日期，格式為 MM/DD")
    weighted_index: IndexData = Field(..., description="加權指數相關資料")
    otc_index: IndexData = Field(..., description="櫃買指數相關資料")
    

class Sector(BaseModel):
    category_name: str = Field(..., description="類股名稱")
    percent_change: str = Field(..., description="報告當日與前一日的百分比變化，要有百分比符號，但是不需要正負號")
    percent_trend: str = Field(..., description="百分比變化趨勢，可能值：up 或 down")

class StrongWeekStack(BaseModel):
    report_date: str = Field(..., description="報告的前一天日期，格式為 MM/DD")
    strong_stocks: List[Sector] = Field(..., description="強勢類股資料（一定要回傳三個）")
    weak_stocks: List[Sector] = Field(..., description="弱勢類股資料（一定要回傳三個）")
    
class NewsItem(BaseModel):
    content: str
    source: Optional[str] = None


class NewsResponse(BaseModel):
    news: List[NewsItem]
    
    
class UsIndex(BaseModel):
    report_date: str = Field(..., description="報告發佈日期，格式為 MM/DD")
    title: str = Field(..., description="報告標題，例如：美股晨訊")
    previous_date: str = Field(..., description="報告前一交易日日期，格式為 MM/DD")
    dow_jones_index: IndexData = Field(None, description="道瓊指數資料")
    sp500_index: IndexData = Field(None, description="S&P 500 指數資料")
    nasdaq_index: IndexData = Field(None, description="那斯達克指數資料")
    sox_index: IndexData = Field(None, description="費城半導體指數資料")
    

class Opinion(BaseModel):
    paragraph_content: str = Field(..., description="萃取出的最符合大盤看法的段落的全部內容")
    

class TitleModel(BaseModel):
    title: str = Field(..., description="簡化後的新聞標題")
    
    
class NewsSummary(BaseModel):
    summary: str

class US_NEWS_SUM(BaseModel):
    summaries: List[NewsSummary] = Field(..., description="三個新聞摘要，每個摘要字數不超過24字元，但是要儘量接近24字元")