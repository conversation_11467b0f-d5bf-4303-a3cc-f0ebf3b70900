#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日股市數據自動化處理腳本
功能：
1. 每天早上7點執行 Get_SFTP_Data.py 獲取數據
2. 自動處理獲取到的台股和美股文件
3. 調用相應的 API 進行分析
"""

import os
import sys
import asyncio
import aiohttp
import subprocess
import schedule
import time
from pathlib import Path
from typing import List, Tuple, Optional
import logging
import json
from datetime import datetime, timedelta

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/ai_env/sam/project/stock_img/log/daily_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置
BASE_DIR = Path("/home/<USER>/ai_env/sam/project/stock_img")
SFTP_SCRIPT = BASE_DIR / "Get_SFTP_Data.py"
RECOG_DATA_DIR = BASE_DIR / "recog_data"
API_BASE_URL = "http://localhost:4512"  # 根據你的實際API地址調整

class StockDataProcessor:
    def __init__(self):
        # self.today = datetime.now().strftime("%Y%m%d")
        # self.data_dir = RECOG_DATA_DIR / self.today
        from datetime import datetime, timedelta
    
        today = datetime.now()
        # 如果是週一，使用上週五的日期
        if today.weekday() == 0:  # 週一是0
            target_date = today - timedelta(days=3)  # 往前推3天到週五
        else:
            target_date = today
        
        self.today = target_date.strftime("%Y%m%d")
        self.data_dir = RECOG_DATA_DIR / self.today
        
    async def execute_sftp_script(self) -> bool:
        """執行 Get_SFTP_Data.py 腳本"""
        try:
            logger.info("開始執行 Get_SFTP_Data.py 腳本")
            
            # 使用 subprocess 執行 Python 腳本
            result = subprocess.run(
                [sys.executable, str(SFTP_SCRIPT)],
                cwd=str(BASE_DIR),
                capture_output=True,
                text=True,
                timeout=300  # 5分鐘超時
            )
            
            if result.returncode == 0:
                logger.info("Get_SFTP_Data.py 執行成功")
                logger.info(f"輸出: {result.stdout}")
                return True
            else:
                logger.error(f"Get_SFTP_Data.py 執行失敗: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Get_SFTP_Data.py 執行超時")
            return False
        except Exception as e:
            logger.error(f"執行 Get_SFTP_Data.py 時發生錯誤: {str(e)}")
            return False
    
    def find_files(self) -> Tuple[List[Path], List[Path]]:
        """找到台股和美股文件"""
        if not self.data_dir.exists():
            logger.error(f"數據目錄不存在: {self.data_dir}")
            return [], []
        
        all_files = list(self.data_dir.glob("*"))
        logger.info(f"找到文件: {[f.name for f in all_files]}")
        
        taiwan_files = [f for f in all_files if f.name.startswith("台股")]
        us_files = [f for f in all_files if f.name.startswith("美股")]
        
        logger.info(f"台股文件: {[f.name for f in taiwan_files]}")
        logger.info(f"美股文件: {[f.name for f in us_files]}")
        
        return taiwan_files, us_files
    
    def validate_files(self, files: List[Path]) -> Tuple[Optional[Path], Optional[Path]]:
        """驗證文件並返回 txt 和 docx 文件"""
        txt_file = None
        docx_file = None
        
        for file in files:
            if file.suffix.lower() == '.txt':
                txt_file = file
            elif file.suffix.lower() == '.docx':
                docx_file = file
        
        if not txt_file or not docx_file:
            logger.error(f"文件不完整，需要同時有 .txt 和 .docx 文件")
            logger.error(f"找到的文件: {[f.name for f in files]}")
            return None, None
        
        return txt_file, docx_file
    
    async def upload_files_to_api(self, txt_file: Path, docx_file: Path, endpoint: str) -> Optional[str]:
        """上傳文件到 API 並返回 task_id"""
        try:
            url = f"{API_BASE_URL}/{endpoint}"
            
            # 準備文件數據
            with open(txt_file, 'rb') as txt_f, open(docx_file, 'rb') as docx_f:
                files = {
                    'files': (txt_file.name, txt_f.read(), 'text/plain'),
                    'files': (docx_file.name, docx_f.read(), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
                }
                
                # 使用 aiohttp 上傳文件
                async with aiohttp.ClientSession() as session:
                    data = aiohttp.FormData()
                    
                    # 添加 txt 文件
                    with open(txt_file, 'rb') as f:
                        data.add_field('files', f.read(), filename=txt_file.name, content_type='text/plain')
                    
                    # 添加 docx 文件
                    with open(docx_file, 'rb') as f:
                        data.add_field('files', f.read(), filename=docx_file.name, 
                                     content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
                    
                    async with session.post(url, data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            task_id = result.get('task_id')
                            logger.info(f"成功提交任務到 {endpoint}，task_id: {task_id}")
                            return task_id
                        else:
                            error_text = await response.text()
                            logger.error(f"API 調用失敗 ({endpoint}): {response.status} - {error_text}")
                            return None
                            
        except Exception as e:
            logger.error(f"上傳文件到 {endpoint} 時發生錯誤: {str(e)}")
            return None
    
    async def wait_for_task_completion(self, task_id: str, task_type: str) -> bool:
        """等待任務完成"""
        max_wait_time = 1800  # 30分鐘最大等待時間
        check_interval = 30   # 每30秒檢查一次
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                url = f"{API_BASE_URL}/task_status/{task_id}"
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            result = await response.json()
                            status = result.get('status')
                            progress = result.get('progress', 0)
                            
                            logger.info(f"{task_type} 任務 {task_id} 狀態: {status}, 進度: {progress}%")
                            
                            if status == 'completed':
                                logger.info(f"{task_type} 任務完成")
                                return True
                            elif status == 'failed':
                                error = result.get('error', '未知錯誤')
                                logger.error(f"{task_type} 任務失敗: {error}")
                                return False
                        else:
                            logger.error(f"檢查任務狀態失敗: {response.status}")
                            
            except Exception as e:
                logger.error(f"檢查任務狀態時發生錯誤: {str(e)}")
            
            await asyncio.sleep(check_interval)
        
        logger.error(f"{task_type} 任務超時")
        return False
    
    async def process_market_data(self, files: List[Path], market_type: str, endpoint: str) -> Tuple[bool, Optional[str]]:
        """處理特定市場的數據"""
        if not files:
            logger.info(f"未找到{market_type}數據文件，跳過處理")
            return True, None
        
        txt_file, docx_file = self.validate_files(files)
        if not txt_file or not docx_file:
            logger.error(f"{market_type}文件不完整，跳過處理")
            return False, None
        
        logger.info(f"開始處理{market_type}文件: {txt_file.name}, {docx_file.name}")
        task_id = await self.upload_files_to_api(txt_file, docx_file, endpoint)
        
        if not task_id:
            logger.error(f"{market_type}文件上傳失敗")
            return False, None
        
        logger.info(f"{market_type}任務已提交，task_id: {task_id}")
        return True, task_id
    
    async def process_daily_data(self):
        """處理每日數據的主要流程（並行處理台股和美股）"""
        logger.info("=== 開始每日股市數據處理 ===")
        
        # 1. 執行 SFTP 腳本獲取數據
        if not await self.execute_sftp_script():
            logger.error("無法獲取 SFTP 數據，終止處理")
            return False
        
        # 2. 尋找文件
        taiwan_files, us_files = self.find_files()
        
        if not taiwan_files and not us_files:
            logger.error("未找到任何股市數據文件")
            return False
        
        # 3. 並行提交台股和美股任務
        logger.info("開始並行處理台股和美股數據...")
        
        # 同時處理兩個市場的數據提交
        results = await asyncio.gather(
            self.process_market_data(taiwan_files, "台股", "stock_index_async"),
            self.process_market_data(us_files, "美股", "uss_index_async"),
            return_exceptions=True
        )
        
        # 解析結果
        taiwan_result = results[0] if not isinstance(results[0], Exception) else (False, None)
        us_result = results[1] if not isinstance(results[1], Exception) else (False, None)
        
        taiwan_upload_success, taiwan_task_id = taiwan_result
        us_upload_success, us_task_id = us_result
        
        # 檢查上傳結果
        if not taiwan_upload_success and not us_upload_success:
            logger.error("台股和美股數據上傳都失敗")
            return False
        
        # 4. 並行等待任務完成
        wait_tasks = []
        task_info = []
        
        if taiwan_upload_success and taiwan_task_id:
            wait_tasks.append(self.wait_for_task_completion(taiwan_task_id, "台股"))
            task_info.append(("台股", taiwan_task_id))
        
        if us_upload_success and us_task_id:
            wait_tasks.append(self.wait_for_task_completion(us_task_id, "美股"))
            task_info.append(("美股", us_task_id))
        
        if wait_tasks:
            logger.info(f"開始並行等待 {len(wait_tasks)} 個任務完成...")
            completion_results = await asyncio.gather(*wait_tasks, return_exceptions=True)
            
            # 檢查完成結果
            taiwan_success = True
            us_success = True
            
            for i, (market_type, task_id) in enumerate(task_info):
                if i < len(completion_results):
                    result = completion_results[i]
                    if isinstance(result, Exception):
                        logger.error(f"{market_type}任務等待過程中發生錯誤: {str(result)}")
                        if market_type == "台股":
                            taiwan_success = False
                        else:
                            us_success = False
                    elif not result:
                        logger.error(f"{market_type}任務完成失敗")
                        if market_type == "台股":
                            taiwan_success = False
                        else:
                            us_success = False
                    else:
                        logger.info(f"{market_type}任務完成成功")
        else:
            logger.error("沒有有效的任務需要等待")
            return False
        
        # 5. 總結
        if taiwan_files and us_files:
            # 兩個市場都有數據
            if taiwan_success and us_success:
                logger.info("=== 台股和美股數據處理都完成 ===")
                return True
            else:
                logger.error(f"=== 部分市場數據處理失敗 === 台股: {taiwan_success}, 美股: {us_success}")
                return taiwan_success or us_success  # 只要有一個成功就算部分成功
        elif taiwan_files:
            # 只有台股數據
            if taiwan_success:
                logger.info("=== 台股數據處理完成 ===")
                return True
            else:
                logger.error("=== 台股數據處理失敗 ===")
                return False
        elif us_files:
            # 只有美股數據
            if us_success:
                logger.info("=== 美股數據處理完成 ===")
                return True
            else:
                logger.error("=== 美股數據處理失敗 ===")
                return False
        else:
            logger.error("=== 沒有找到任何數據文件 ===")
            return False

def run_daily_task():
    """執行每日任務的包裝函數"""
    processor = StockDataProcessor()
    
    try:
        # 運行異步任務
        result = asyncio.run(processor.process_daily_data())
        
        if result:
            logger.info("每日任務執行成功")
        else:
            logger.error("每日任務執行失敗")
            
    except Exception as e:
        logger.error(f"執行每日任務時發生未預期的錯誤: {str(e)}")

def main():
    """主函數"""
    logger.info("股市數據自動化處理系統啟動")
    
    # 設置每天下午1點31分執行任務
    # schedule.every().day.at("13:31").do(run_daily_task)
    schedule.every().monday.at("13:31").do(run_daily_task)
    schedule.every().tuesday.at("13:31").do(run_daily_task)
    schedule.every().wednesday.at("13:31").do(run_daily_task)
    schedule.every().thursday.at("13:31").do(run_daily_task)
    schedule.every().friday.at("13:31").do(run_daily_task)
        
    logger.info("已設置每日 13:31 執行任務")
    
    # 如果需要立即測試，可以取消註釋以下行
    # logger.info("立即執行一次測試...")
    run_daily_task()
    
    # 持續運行調度器
    while True:
        schedule.run_pending()
        time.sleep(60)  # 每分鐘檢查一次

if __name__ == "__main__":
    main()